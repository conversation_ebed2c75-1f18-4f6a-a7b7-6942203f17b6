package com.yykj.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yykj.app.dto.GroupVo;
import com.yykj.app.dto.PersonRoleVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-16
 */
@TableName("Person")
@Data
public class Person implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 物资系统对应用户ID
     */
    @TableField("WZ_UserID")
    private Integer wzUserid;

    /**
     * 用于判断是集团企业还是施工单位(管理员1，其他是2)
     */
    private Integer type;

    /**
     * 短号
     */
    @TableField("Sphone")
    private String sphone;

    /**
     * 是否有签名照
     */
    @TableField("PhName")
    private Integer phName;

    @TableField("Telephone")
    private String telephone;

    @TableId(value = "Id", type = IdType.AUTO)
    private Integer id;

    @JsonIgnore
    @TableField("Password")
    private String password;

    private Integer zjCs;

    @TableField("LoginName")
    private String loginName;

    @TableField("GroupID")
    private Integer groupID;
    /**
     * 头像
     */
    @TableField(exist = false)
    private String photo;
    /**
     * 单位名称
     */
    @TableField(exist = false)
    private String groupName;
    /**
     * 单位简称
     */
    @TableField(exist = false)
    private String groupQc;
    /**
     * 顶级单位名称
     */
    @TableField(exist = false)
    private String topGroupName;
    @TableField(exist = false)
    private Integer topGroupId;
    /**
     * 顶级单位名称 人资
     */
    @TableField(exist = false)
    private String topGroupNameRz;
    @TableField(exist = false)
    private Integer topGroupIdRz;
    /**
     * 上级单位名称
     */
    @TableField(exist = false)
    private String praentGroupName;
    @TableField(exist = false)
    private Integer praentGroupId;

    /**
     * 上级单位名称 人资
     */
    @TableField(exist = false)
    private String praentGroupNameRz;
    @TableField(exist = false)
    private Integer praentGroupIdRz;
    @TableField(exist = false)
    private String topGroupShortPinyin;

    @TableField("MsgType")
    private String msgType;

    private String oa;

    @TableField("RealName")
    private String realName;

    @TableField("RoleId")
    private Integer roleId;

    @TableField("CertificateID")
    private String certificateID;

    @TableField("OfficePhone")
    private String OfficePhone;
    @TableField("P_XH")
    private Integer xh;

    @TableField(exist = false)
    private boolean init = false;

    @TableField(exist = false)
    private String address;

    @TableField(exist = false)
    private List<PersonRoleVO> personRoles;
    /**
     * 单位级别类型
     */
    @TableField(exist = false)
    private Integer groupType;

    public void setGroupVo(GroupVo vo) {
        if (vo != null) {
            this.setTopGroupId(vo.getTopGroupId());
            this.setPraentGroupId(vo.getPraentGroupId());
            this.setPraentGroupName(vo.getPraentGroupName());
            this.setTopGroupName(vo.getTopGroupName());
            this.setGroupName(vo.getGroupName());
            this.setGroupQc(vo.getGroupQc());
            this.setTopGroupIdRz(vo.getTopGroupIdRz());
            this.setTopGroupNameRz(vo.getTopGroupNameRz());
            this.setPraentGroupIdRz(vo.getPraentGroupIdRz());
            this.setPraentGroupNameRz(vo.getPraentGroupNameRz());
            this.setTopGroupShortPinyin(vo.getTopGroupShortPinyin());
            this.setAddress(vo.getAddress());
            this.setGroupType(vo.getGroupType());
        }
    }
}
