package com.yykj.szyy.flow.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.stream.*;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.stream.StreamListener;
import org.springframework.data.redis.stream.StreamMessageListenerContainer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.time.Duration;

@Slf4j
@Configuration
public class RedisStreamConsumerConfig {

    @Autowired
    ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    RedisStreamConfig redisStreamConfig;

    /**
     * 主要做的是将OrderStreamListener监听绑定消费者，用于接收消息
     *
     * @param connectionFactory
     * @param streamListener
     * @return
     */
    @Bean
    public StreamMessageListenerContainer<String, MapRecord<String, String, String>> consumerListener1(RedisConnectionFactory connectionFactory, OrderStreamListener streamListener) {
        StreamMessageListenerContainer<String, MapRecord<String, String, String>> container = streamContainer(redisStreamConfig.getStream(),"consumer-1", connectionFactory, streamListener);

        container.start();
        return container;
    }
    @Bean
    public StreamMessageListenerContainer<String, MapRecord<String, String, String>> consumerListener2(RedisConnectionFactory connectionFactory, OrderStreamListener streamListener) {
        StreamMessageListenerContainer<String, MapRecord<String, String, String>> container = streamContainer(redisStreamConfig.getStream(), "consumer-2",connectionFactory, streamListener);
        container.start();
        return container;
    }

    /**
     * @param mystream          从哪个流接收数据
     * @param connectionFactory
     * @param streamListener    绑定的监听类
     * @return
     */
    private StreamMessageListenerContainer<String, MapRecord<String, String, String>> streamContainer(String mystream, String cus, RedisConnectionFactory connectionFactory, StreamListener<String, MapRecord<String, String, String>> streamListener) {
        StreamMessageListenerContainer.StreamMessageListenerContainerOptions<String, MapRecord<String, String, String>> options =
                StreamMessageListenerContainer.StreamMessageListenerContainerOptions.builder()
                .pollTimeout(Duration.ofSeconds(20)) // 拉取消息超时时间
                .batchSize(10) // 批量抓取消
                .serializer(RedisSerializer.string())
                .executor(threadPoolTaskExecutor).build();
        StreamMessageListenerContainer<String, MapRecord<String, String, String>> container = StreamMessageListenerContainer.create(connectionFactory, options);
        //指定消费最新的消息
        StreamOffset<String> offset = StreamOffset.create(mystream, ReadOffset.lastConsumed());
        //创建消费者
        Consumer consumer = Consumer.from(redisStreamConfig.getGroup(), cus);
        StreamMessageListenerContainer.StreamReadRequest<String> streamReadRequest = StreamMessageListenerContainer.StreamReadRequest.builder(offset).errorHandler((error) -> {
            log.error("阅读消息error ", error);
        }).cancelOnError(e -> false).consumer(consumer).autoAcknowledge(false).build();
        //指定消费者对象
        container.register(streamReadRequest, streamListener);
        return container;
    }

}
