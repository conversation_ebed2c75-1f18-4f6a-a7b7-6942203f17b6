package com.yykj.szyy.flow.config;

import cn.hutool.core.date.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.stream.MapRecord;
import org.springframework.data.redis.connection.stream.RecordId;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.stream.StreamListener;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Map;

@Component
@Slf4j
public class OrderStreamListener implements StreamListener<String, MapRecord<String, String, String>> {
    @Autowired
    StringRedisTemplate stringRedisTemplate;

    @Autowired
    RedisStreamConfig redisStreamConfig;

    @Override
    public void onMessage(MapRecord<String, String, String> message) {
        try{
            // 消息ID
            RecordId messageId = message.getId();
            Duration between = LocalDateTimeUtil.between(LocalDateTimeUtil.of(messageId.getTimestamp()) , LocalDateTimeUtil.now());
            //过滤 10 分钟以上未消费的数据
            if (between.toMillis()<=10){
                Map<String, String> content = message.getValue();
                String from = content.get("from");
                if (from.equals("910201")){
                    log.info("StreamMessageListener stream messageId={}, from={}, body={}", messageId, from, content);
                    // 通过RedisTemplate手动确认消息
                    this.stringRedisTemplate.opsForStream().acknowledge(redisStreamConfig.getGroup(), message);
                }
            }
            // 消息的key和value
        }catch (Exception e){
            // 处理异常
            e.printStackTrace();
        }

    }
}
