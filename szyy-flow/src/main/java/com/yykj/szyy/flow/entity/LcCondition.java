package com.yykj.szyy.flow.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 流程条件配置信息
 */
@TableName(value ="Lc_Condition")
@Data
public class LcCondition implements Serializable {
    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 上级ID
     */
    private Integer pid;

    /**
     * 流程定义ID
     */
    private Integer lcDefineid;

    /**
     * 流程阶段ID
     */
    private Integer jdid;

    /**
     * 流程阶段名称
     */
    private String jdmc;

    /**
     * 下一阶段ID
     */
    private Integer nextjdid;

    /**
     * 下一阶段名称
     */
    private String nextjdmc;

    /**
     * 业务表
     */
    private String ywb;

    /**
     * 判断条件
     * 默认语句，后续加上EL表达式
     */
    private String condition;

    /**
     * 判断取值
     */
    private String lConditionCode;

    /**
     * 条件左边
     */
    private String lCondition;

    /**
     * 操作符
     */
    private String zCondition;

    /**
     * 条件右边
     */
    private String rCondition;

    /**
     * 
     */
    private String fromjscdt;

    /**
     * 是否并行
     */
    private Integer isbx;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        LcCondition other = (LcCondition) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getPid() == null ? other.getPid() == null : this.getPid().equals(other.getPid()))
            && (this.getLcDefineid() == null ? other.getLcDefineid() == null : this.getLcDefineid().equals(other.getLcDefineid()))
            && (this.getJdid() == null ? other.getJdid() == null : this.getJdid().equals(other.getJdid()))
            && (this.getJdmc() == null ? other.getJdmc() == null : this.getJdmc().equals(other.getJdmc()))
            && (this.getNextjdid() == null ? other.getNextjdid() == null : this.getNextjdid().equals(other.getNextjdid()))
            && (this.getNextjdmc() == null ? other.getNextjdmc() == null : this.getNextjdmc().equals(other.getNextjdmc()))
            && (this.getYwb() == null ? other.getYwb() == null : this.getYwb().equals(other.getYwb()))
            && (this.getCondition() == null ? other.getCondition() == null : this.getCondition().equals(other.getCondition()))
            && (this.getLConditionCode() == null ? other.getLConditionCode() == null : this.getLConditionCode().equals(other.getLConditionCode()))
            && (this.getLCondition() == null ? other.getLCondition() == null : this.getLCondition().equals(other.getLCondition()))
            && (this.getZCondition() == null ? other.getZCondition() == null : this.getZCondition().equals(other.getZCondition()))
            && (this.getRCondition() == null ? other.getRCondition() == null : this.getRCondition().equals(other.getRCondition()))
            && (this.getFromjscdt() == null ? other.getFromjscdt() == null : this.getFromjscdt().equals(other.getFromjscdt()))
            && (this.getIsbx() == null ? other.getIsbx() == null : this.getIsbx().equals(other.getIsbx()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getPid() == null) ? 0 : getPid().hashCode());
        result = prime * result + ((getLcDefineid() == null) ? 0 : getLcDefineid().hashCode());
        result = prime * result + ((getJdid() == null) ? 0 : getJdid().hashCode());
        result = prime * result + ((getJdmc() == null) ? 0 : getJdmc().hashCode());
        result = prime * result + ((getNextjdid() == null) ? 0 : getNextjdid().hashCode());
        result = prime * result + ((getNextjdmc() == null) ? 0 : getNextjdmc().hashCode());
        result = prime * result + ((getYwb() == null) ? 0 : getYwb().hashCode());
        result = prime * result + ((getCondition() == null) ? 0 : getCondition().hashCode());
        result = prime * result + ((getLConditionCode() == null) ? 0 : getLConditionCode().hashCode());
        result = prime * result + ((getLCondition() == null) ? 0 : getLCondition().hashCode());
        result = prime * result + ((getZCondition() == null) ? 0 : getZCondition().hashCode());
        result = prime * result + ((getRCondition() == null) ? 0 : getRCondition().hashCode());
        result = prime * result + ((getFromjscdt() == null) ? 0 : getFromjscdt().hashCode());
        result = prime * result + ((getIsbx() == null) ? 0 : getIsbx().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", pid=").append(pid);
        sb.append(", lcDefineid=").append(lcDefineid);
        sb.append(", jdid=").append(jdid);
        sb.append(", jdmc=").append(jdmc);
        sb.append(", nextjdid=").append(nextjdid);
        sb.append(", nextjdmc=").append(nextjdmc);
        sb.append(", ywb=").append(ywb);
        sb.append(", condition=").append(condition);
        sb.append(", lConditionCode=").append(lConditionCode);
        sb.append(", lCondition=").append(lCondition);
        sb.append(", zCondition=").append(zCondition);
        sb.append(", rCondition=").append(rCondition);
        sb.append(", fromjscdt=").append(fromjscdt);
        sb.append(", isbx=").append(isbx);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}