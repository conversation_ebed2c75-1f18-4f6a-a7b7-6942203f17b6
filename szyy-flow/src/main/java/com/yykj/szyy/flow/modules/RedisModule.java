package com.yykj.szyy.flow.modules;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Range;
import org.springframework.data.redis.connection.stream.StreamInfo;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StreamOperations;
import org.springframework.stereotype.Component;
import org.ssssssss.magicapi.core.annotation.MagicModule;

import java.util.List;

@Slf4j
@Component
//注入到Spring容器中
@MagicModule("rs")
public class RedisModule {

//    redisstream:
//    stream: szyy_flow
//    group: flow_group
//    consumer: consumer-1
    @Value("${redisstream.stream}")
    String stream;

    @Value("${redisstream.group}")
    String group;

    @Value("${redisstream.consumer}")
    String consumer;
    @Autowired
    RedisTemplate redisTemplate;

    public String list(){
        StreamInfo.XInfoGroups tGroup = redisTemplate.opsForStream().groups(group);
        return tGroup.toString();
    }

    public String check(){
        StreamOperations sOpts = redisTemplate.opsForStream();
        StreamInfo.XInfoStream sInfo = sOpts.info(stream);
        StreamInfo.XInfoGroups sGroup = sOpts.groups(stream);
        return "";
    }

    public String readAll(){
        StreamOperations sOpts = redisTemplate.opsForStream();
        final List<Object> range = sOpts.range(stream, Range.unbounded());
        return ""+range.size();
    }
}
