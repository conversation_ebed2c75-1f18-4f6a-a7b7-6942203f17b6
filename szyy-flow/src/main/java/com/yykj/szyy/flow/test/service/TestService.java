package com.yykj.szyy.flow.test.service;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.yykj.szyy.flow.config.SentinelExceptionHandler;
import org.springframework.stereotype.Service;

@Service
public class TestService {

    @SentinelResource(value = "sayHello", blockHandler = "handleException", blockHandlerClass = {ExceptionUtil.class})
    public String sayHello(String name) {
        return "Hello, " + name;
    }
}