package com.yykj.szyy.flow.utils;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.yykj.szyy.flow.constants.HttpConstant;
import com.yykj.szyy.flow.constants.SysVar;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.Calendar;
import java.util.Date;

import static com.yykj.szyy.flow.constants.SysVar.EXPIRE_TIME;


/**
 * <AUTHOR>
 * @Date 2018-07-12 14:23
 * @Desc JWT工具类
 **/
public class JwtUtil {

    /**
     * 校验token是否正确
     *
     * @param token  密钥
     * @param secret 用户的密码
     * @return 是否正确
     */
    public static boolean verify(String token, String userName, String secret) {
        try {
            // 根据密码生成JWT效验器
            Algorithm algorithm = Algorithm.HMAC256(secret);
            JWTVerifier verifier = JWT.require(algorithm).withClaim(SysVar.USER_NAME, userName).build();
            // 效验TOKEN
            verifier.verify(token);
            return true;
        } catch (Exception exception) {
            return false;
        }
    }

    /**
     * 获得token中的信息无需secret解密也能获得
     *
     * @return token中包含的用户名
     */
    public static String getUserName(String token) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getClaim(SysVar.USER_NAME).asString();
        } catch (JWTDecodeException e) {
            return null;
        }
    }

    /**
     * 根据request中的token获取用户账号
     *
     * @param request
     * @return
     * @throws RuntimeException
     */
    public static String getUserNameByToken(HttpServletRequest request) throws RuntimeException {
        String accessToken = request.getHeader(HttpConstant.X_ACCESS_TOKEN);
        String username = getUserName(accessToken);
        if (StringUtils.isEmpty(username)) {
            throw new RuntimeException("未获取到用户");
        }
        return username;
    }
    /**
     * 根据request中的token获取用户账号
     *
     * @param request
     * @return
     * @throws RuntimeException
     */
    public static String getToken(HttpServletRequest request) throws RuntimeException {
        return request.getHeader(HttpConstant.X_ACCESS_TOKEN);
    }
    /**
     * 从session中获取变量
     *
     * @param key
     * @return
     */
    public static String getSessionData(HttpServletRequest request,String key) {
        //${myVar}%
        //得到${} 后面的值
        String moshi = "";
        if (key.indexOf("}") != -1) {
            moshi = key.substring(key.indexOf("}") + 1);
        }
        String returnValue = null;
        if (key.contains("#{")) {
            key = key.substring(2, key.indexOf("}"));
        }
        if (!StringUtils.isEmpty(key)) {
            HttpSession session = request.getSession();
            returnValue = (String) session.getAttribute(key);
        }
        //结果加上${} 后面的值
        if (returnValue != null) {
            returnValue = returnValue + moshi;
        }
        return returnValue;
    }

    public static String sign(String userName, String secret,String device, Date time) {
        Algorithm algorithm = Algorithm.HMAC256(secret);
        // 附带username信息
        return JWT.create()
                .withClaim(SysVar.USER_NAME, userName)
                .withClaim(SysVar.DEV_TYPE, device)
                .withExpiresAt(time)
                .withIssuedAt(new Date())
                .sign(algorithm);
    }

    public static String sign(String userName, String secret) {
        return sign(userName,secret,nextSecond(new Date(), EXPIRE_TIME));
    }

    public static Date nextSecond(Date date, int second) {
        Calendar cal = Calendar.getInstance();
        if (date != null) {
            cal.setTime(date);
        }
        cal.add(Calendar.SECOND, second);
        return cal.getTime();
    }

    public static void main(String[] args) {
        System.err.println(JwtUtil.getUserName("eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyTmFtZSI6IjE4MDc0MjI2MTM3IiwiZXhwIjoxNjkwNDQ0MjA3LCJpYXQiOjE2MjczNzIyMDd9.T3nuuyYpwpdrUU1YQ9sB1V6vXSPx2Fr3LMY1wwKzvfc"));
//        String token = JwtUtil.sign("admin",salt,new Date(System.currentTimeMillis()+30000));
//        String token2 = JwtUtil.sign("admin",salt);
//        System.err.println(token);
//        System.err.println(token2);
//        System.out.println(JwtUtil.getUserName(token));
//        System.err.println(JwtUtil.verify(token,"admin",salt));
//        System.err.println(JwtUtil.verify("","admin",salt));
    }

    /**
     * 获得token中的信息无需secret解密也能获得
     *
     * @return token中包含的签发时间
     */
    public static Date getIssuedAt(String token) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getIssuedAt();
        } catch (JWTDecodeException e) {
            return null;
        }
    }

    /**
     * 生成签名,expireTime后过期
     *
     * @param userName 用户名
     * @param time     过期时间s
     * @return 加密的token
     */
    public static String sign(String userName, String secret, Date time) {
        Algorithm algorithm = Algorithm.HMAC256(secret);
        // 附带username信息
        return JWT.create()
                .withClaim(SysVar.USER_NAME, userName)
                .withExpiresAt(time)
                .withIssuedAt(new Date())
                .sign(algorithm);
    }

    /**
     * token是否过期
     *
     * @return true：过期
     */
    public static boolean isTokenExpired(String token) {
        Date now = Calendar.getInstance().getTime();
        DecodedJWT jwt = JWT.decode(token);
        return jwt.getExpiresAt().before(now);
    }

}
