package com.yykj.szyy.flow.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yykj.szyy.flow.entity.LcCondition;
import com.yykj.szyy.flow.service.LcConditionService;
import com.yykj.szyy.flow.mapper.LcConditionMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【Lc_Condition】的数据库操作Service实现
* @createDate 2022-12-21 15:07:36
*/
@Service
public class LcConditionServiceImpl extends ServiceImpl<LcConditionMapper, LcCondition>
    implements LcConditionService{

}




