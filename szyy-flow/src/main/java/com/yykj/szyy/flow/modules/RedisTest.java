package com.yykj.szyy.flow.modules;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.concurrent.atomic.AtomicInteger;

@Service
@Slf4j
public class RedisTest  {

    public RedisTest(RedisTemplate redisTemplate) {
        String lockKey = "SZYY.FLOW.LOCK.9102";
        String lockVal = "OK";
        AtomicInteger i = new AtomicInteger(0);
        redisTemplate.getConnectionFactory().getConnection().subscribe(new MessageListener() {
            @Override
            public void onMessage(Message message, byte[] bytes) {
                log.info("Receive 001: " + message);
                // 收到消息的处理逻辑
                boolean res = redisTemplate.opsForValue().setIfAbsent(lockKey+ message.getBody(), Duration.ofSeconds(10));
                if (res){
                    log.info("Receive message 001: " + message);
                    i.incrementAndGet();
                    System.err.println("  ===>   "+ i.get());
                    redisTemplate.delete(lockKey);
                }
            }
        }, "SZYY.FLOW.SUBMIT".getBytes(StandardCharsets.UTF_8));

//        redisTemplate.getConnectionFactory().getConnection().subscribe(new MessageListener() {
//            @Override
//            public void onMessage(Message message, byte[] bytes) {
//                log.info("Receive 002: " + message);
//                // 收到消息的处理逻辑
//                boolean res = redisTemplate.opsForValue().setIfAbsent(lockKey+message.getBody(), Duration.ofSeconds(300));
//                if (res){
//                    log.info("Receive message 002: " + message);
//                    i.incrementAndGet();
//                    System.err.println("  ===>   "+ i.get());
//                }
//            }
//        }, "SZYY.FLOW.SUBMIT".getBytes(StandardCharsets.UTF_8));
    }

}
