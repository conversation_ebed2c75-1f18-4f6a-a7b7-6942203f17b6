package com.yykj.szyy.flow.modules;

import com.yykj.app.entity.Person;
import com.yykj.szyy.flow.constants.HttpConstant;
import com.yykj.szyy.flow.constants.SysVar;
import com.yykj.szyy.flow.utils.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Component;
import org.ssssssss.magicapi.core.annotation.MagicModule;
import org.ssssssss.magicapi.core.context.RequestContext;

@Slf4j
@Component  //注入到Spring容器中
@MagicModule("user")
public class UserFunctions {

    @Autowired  // 注入MagicAPIService对象
    private CacheManager cacheManager;


    /**
     * 返回模块名，使用时通过import指令导入之后使用
     */
    public Person user() {
        String token = RequestContext.getHttpServletRequest().getHeader(HttpConstant.X_ACCESS_TOKEN);
        Cache cache = cacheManager.getCache(SysVar.AUTH);
        return cache.get(JwtUtil.getUserName(token), Person.class);
    }

    public String loginName(){
        String token = RequestContext.getHttpServletRequest().getHeader(HttpConstant.X_ACCESS_TOKEN);
        return JwtUtil.getUserName(token);
    }

    public String mock(String str){
        String token = JwtUtil.sign(str, SysVar.SALT);
        Cache tokenCache = this.cacheManager.getCache(SysVar.TOKEN);
        Cache userCache = this.cacheManager.getCache(SysVar.AUTH);



        userCache.put(str, null);
        tokenCache.put(str, token);
        return token;
    }
}