package com.yykj.szyy.flow.config;

import com.yykj.app.dto.PersonRoleVO;
import com.yykj.app.entity.Person;
import com.yykj.szyy.flow.base.Result;
import com.yykj.szyy.flow.constants.HttpConstant;
import com.yykj.szyy.flow.utils.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.ssssssss.magicapi.core.interceptor.RequestInterceptor;
import org.ssssssss.magicapi.core.model.ApiInfo;
import org.ssssssss.magicapi.core.model.Options;
import org.ssssssss.script.MagicScriptContext;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class RoleConfig implements RequestInterceptor {

    @Autowired
    CacheManager cacheManager;

    @Override
    public Object preHandle(ApiInfo info, MagicScriptContext context, HttpServletRequest request, HttpServletResponse response) throws Exception {
        String map = info.getOptionValue(Options.ANONYMOUS);
        if ("true".equalsIgnoreCase(map)) {
            return null;
        }
        String token = request.getHeader(HttpConstant.X_ACCESS_TOKEN);
        if (StringUtils.isEmpty(token)) {
            return Result.error(401, "token异常");
        }

        if (StringUtils.isEmpty(JwtUtil.getUserName(token))) {
            return Result.error(401, "token异常!");
        }
        Cache tokenCache = cacheManager.getCache("TOKEN");
        if (tokenCache == null) {
            return Result.error(401, "token异常!!");
        }

        if (!StringUtils.isEmpty(info.getOptionValue(Options.ROLE)) ) {
            Cache personCache = cacheManager.getCache("AUTH");
            Person nPerson = personCache.get(JwtUtil.getUserName(token), Person.class);
            List<PersonRoleVO> list = nPerson.getPersonRoles();

            if (list == null || list.size() == 0) {
                return Result.error(401, "token异常");
            } else {
                List<String> roleNames = list.stream().map(PersonRoleVO::getRoleName).collect(Collectors.toList());
                String roles = info.getOptionValue(Options.ROLE);
                if (!StringUtils.isEmpty(roles) ){
                    if (Arrays.stream(roles.split(",")).allMatch(role->roleNames.contains(role))){
                        return Result.error(500, "权限不足!");
                    }
                }
            }
        }
        return null;
    }
}
