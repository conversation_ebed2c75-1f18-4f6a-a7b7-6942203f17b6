package com.yykj.szyy.flow.modules;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.ssssssss.magicapi.core.annotation.MagicModule;
import org.ssssssss.magicapi.utils.JsonUtils;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
//注入到Spring容器中
@MagicModule("message")
public class MessageModule {

    @Value("${message.appid}")
    private String appid;
    /**
     * 返回模块名，使用时通过import指令导入之后使用
     */
    public boolean sendMessage(String phones, String message) {
        Map<String,Object> map = new HashMap<>();
        map.put("appId", appid);
        map.put("mobile", phones);
        map.put("message", message);
        try {
            String res = HttpRequest.post("http://dx.sc.ydy.com:55055/mas/api/sendMessage").form(map)
               .contentType("application/x-www-form-urlencoded").timeout(6000).execute().body();
            log.info(res);
            JSONObject object = JSON.parseObject(res);
            //{"success":true,"data":"00000000000000000000"}
            if (object.getBoolean("success")){
                return true;
            }else{
                return false;
            }
        }catch (Exception e){
            log.error("短信发送失败：{},原因：{}",message, e.getMessage());
            return false;
        }
    }



}
