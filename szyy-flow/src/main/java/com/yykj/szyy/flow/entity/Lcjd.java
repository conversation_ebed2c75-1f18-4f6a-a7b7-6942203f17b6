package com.yykj.szyy.flow.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 流程阶段
 * @TableName Lcjd
 */
@TableName(value ="Lcjd")
@Data
public class Lcjd implements Serializable {
    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 流程阶段ID
     */
    private Integer lcjdid;

    /**
     * 流程定义ID
     */
    private Integer lcDefineid;

    /**
     * 阶段名称
     */
    private String jdmc;

    /**
     * 下一阶段ID(作废)
     */
    private Integer nextid;

    /**
     * 排序
     */
    private Integer type;

    /**
     * 
     */
    private Integer shgr;

    /**
     * 单位ID
     */
    private String groupid;

    /**
     * 是否并行
     */
    private Integer isbx;

    /**
     * 表单信息
     */
    private String formtype;

    /**
     * 检查附件配置(当前)
     */
    private String checkfile;

    /**
     * 检查数据配置
     */
    private String checkdata;

    /**
     * 是否可以回退
     */
    private Integer canback;

    /**
     * 回退阶段ID
     */
    private String backjdid;

    /**
     * 检查附件配置(既往)
     */
    private String lookfilejdid;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        Lcjd other = (Lcjd) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getLcjdid() == null ? other.getLcjdid() == null : this.getLcjdid().equals(other.getLcjdid()))
            && (this.getLcDefineid() == null ? other.getLcDefineid() == null : this.getLcDefineid().equals(other.getLcDefineid()))
            && (this.getJdmc() == null ? other.getJdmc() == null : this.getJdmc().equals(other.getJdmc()))
            && (this.getNextid() == null ? other.getNextid() == null : this.getNextid().equals(other.getNextid()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
            && (this.getShgr() == null ? other.getShgr() == null : this.getShgr().equals(other.getShgr()))
            && (this.getGroupid() == null ? other.getGroupid() == null : this.getGroupid().equals(other.getGroupid()))
            && (this.getIsbx() == null ? other.getIsbx() == null : this.getIsbx().equals(other.getIsbx()))
            && (this.getFormtype() == null ? other.getFormtype() == null : this.getFormtype().equals(other.getFormtype()))
            && (this.getCheckfile() == null ? other.getCheckfile() == null : this.getCheckfile().equals(other.getCheckfile()))
            && (this.getCheckdata() == null ? other.getCheckdata() == null : this.getCheckdata().equals(other.getCheckdata()))
            && (this.getCanback() == null ? other.getCanback() == null : this.getCanback().equals(other.getCanback()))
            && (this.getBackjdid() == null ? other.getBackjdid() == null : this.getBackjdid().equals(other.getBackjdid()))
            && (this.getLookfilejdid() == null ? other.getLookfilejdid() == null : this.getLookfilejdid().equals(other.getLookfilejdid()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getLcjdid() == null) ? 0 : getLcjdid().hashCode());
        result = prime * result + ((getLcDefineid() == null) ? 0 : getLcDefineid().hashCode());
        result = prime * result + ((getJdmc() == null) ? 0 : getJdmc().hashCode());
        result = prime * result + ((getNextid() == null) ? 0 : getNextid().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getShgr() == null) ? 0 : getShgr().hashCode());
        result = prime * result + ((getGroupid() == null) ? 0 : getGroupid().hashCode());
        result = prime * result + ((getIsbx() == null) ? 0 : getIsbx().hashCode());
        result = prime * result + ((getFormtype() == null) ? 0 : getFormtype().hashCode());
        result = prime * result + ((getCheckfile() == null) ? 0 : getCheckfile().hashCode());
        result = prime * result + ((getCheckdata() == null) ? 0 : getCheckdata().hashCode());
        result = prime * result + ((getCanback() == null) ? 0 : getCanback().hashCode());
        result = prime * result + ((getBackjdid() == null) ? 0 : getBackjdid().hashCode());
        result = prime * result + ((getLookfilejdid() == null) ? 0 : getLookfilejdid().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", lcjdid=").append(lcjdid);
        sb.append(", lcDefineid=").append(lcDefineid);
        sb.append(", jdmc=").append(jdmc);
        sb.append(", nextid=").append(nextid);
        sb.append(", type=").append(type);
        sb.append(", shgr=").append(shgr);
        sb.append(", groupid=").append(groupid);
        sb.append(", isbx=").append(isbx);
        sb.append(", formtype=").append(formtype);
        sb.append(", checkfile=").append(checkfile);
        sb.append(", checkdata=").append(checkdata);
        sb.append(", canback=").append(canback);
        sb.append(", backjdid=").append(backjdid);
        sb.append(", lookfilejdid=").append(lookfilejdid);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}