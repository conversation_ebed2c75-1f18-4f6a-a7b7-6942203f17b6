package com.yykj.szyy.flow;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.context.config.annotation.RefreshScope;

@SpringBootApplication
@EnableDiscoveryClient
@RefreshScope
@MapperScan({"com.yykj.szyy.mapper","com.yykj.szyy.*.mapper","com.yykj.szyy.flow.mapper.*"})
@EnableCaching
public class SzyyFlowApplication {

    public static void main(String[] args) {
        SpringApplication.run(SzyyFlowApplication.class, args);
    }

}
