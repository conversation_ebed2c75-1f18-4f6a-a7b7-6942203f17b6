package com.yykj.szyy.flow.test.controller;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.yykj.szyy.flow.config.MyConfig;
import com.yykj.szyy.flow.test.service.TestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.annotation.RequestScope;

@RestController
@RequestMapping("/")
@Slf4j
public class ConfigController {

    @Autowired
    private MyConfig cfg;

    @Value("${errorone.mynum}")
    Integer mynum;

    @RestController
    public class EchoController {
        @GetMapping(value = "echo")
        public String echo() {
            log.info("Nacos Config {}", mynum);
            log.info("Nacos Config3 {}", cfg.getMynum());

            return "";
        }
    }

    @Autowired
    private TestService service;

    @GetMapping(value = "/hello/{name}")
    public String apiHello(@PathVariable String name) {
        return service.sayHello(name);
    }
}