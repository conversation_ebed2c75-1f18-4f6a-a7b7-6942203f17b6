errorone:
  mynum: 120
spring:
  cloud:
    nacos:
      discovery:
        server-addr: **************:8848
        namespace: 4c23c009-10d2-4c0a-a90e-07a1a907699f
        contextPath: /nacos
        password: nacos
        username: nacos
    sentinel:
      eager: true
      transport:
        port: 8719
        heartbeat-interval-ms: 500
        dashboard: **************:8858
  redis:
    host: ************** #***************
    port: 6379
    timeout: 30000
      # 密码
    password: davice@252
    database: 0
    lettuce:
      pool:
        max-active: 28
        min-idle: 4
  cache:
    type: redis
  main:
    allow-bean-definition-overriding: true
  datasource:
    name: druidDataSource
    type: com.alibaba.druid.pool.DruidDataSource
    dynamic:
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      druid:
        max-active: 20
        initial-size: 10
        max-wait: 60000
        min-idle: 10
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        validation-query: select 1
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
        pool-prepared-statements: true
        max-pool-prepared-statement-per-connection-size: 20
      datasource:
        master:
          driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
          url: ***********************************************************************************************************
          username: topdavice1
          password: topdavice@---
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB
server:
  port: 9529
  max-http-header-size: 4048576

mybatis-plus:
  mapper-locations: classpath:mapper/*.xml,classpath:mapper/*/*.xml #实体类所要映射sql的xml文件
  type-aliases-package: com.yykj.szyy.flow.mapper,com.yykj.szyy.flow.*.mapper #实体类全路径所在的最后一级目录
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  #config-location: classpath:/mybatis-config.xml

magic-api:
  web: "/errorone/cn"
  auto-import-module: db,user
  allow-override: false #禁止覆盖应用接口
  sql-column-case: camel #启用驼峰命名转换
  show-url: true
  banner: false
  resource:
    type: file
    location: ./szyy-flow/data/flow
  backup:
    resource-type: file
    location: ./szyy-flow/data/flow_bak
  security:
    username: szyy
    password: szyy_2031214@123
  response: |- #配置JSON格式，格式为magic-script中的表达式
    {
      code: code,
      message: message,
      result: data,
      success: code == 200,
      timestamp
    }
  response-code:
    success: 200 #执行成功的code值
    invalid: 500 #参数验证未通过的code值
    exception: 500 #执行出现异常的code值
  editor-config: classpath:./magic-editor-config.js
redisstream:
  stream: szyy_flow
  group: flow_group
  consumer: consumer-1
management:
  endpoints:
    web:
      exposure:
        include: "*"
message:
  appid: 00000000-0000-0000-0000-000000000000
