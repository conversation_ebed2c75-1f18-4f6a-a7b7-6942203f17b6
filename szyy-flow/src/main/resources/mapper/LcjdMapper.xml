<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yykj.szyy.flow.mapper.LcjdMapper">

    <resultMap id="BaseResultMap" type="com.yykj.szyy.flow.entity.Lcjd">
            <id property="id" column="ID" jdbcType="INTEGER"/>
            <result property="lcjdid" column="lcjdID" jdbcType="INTEGER"/>
            <result property="lcDefineid" column="lc_defineID" jdbcType="INTEGER"/>
            <result property="jdmc" column="jdmc" jdbcType="VARCHAR"/>
            <result property="nextid" column="nextID" jdbcType="INTEGER"/>
            <result property="type" column="type" jdbcType="INTEGER"/>
            <result property="shgr" column="shgr" jdbcType="INTEGER"/>
            <result property="groupid" column="GroupID" jdbcType="VARCHAR"/>
            <result property="isbx" column="IsBX" jdbcType="INTEGER"/>
            <result property="formtype" column="FormType" jdbcType="VARCHAR"/>
            <result property="checkfile" column="CheckFile" jdbcType="VARCHAR"/>
            <result property="checkdata" column="CheckData" jdbcType="VARCHAR"/>
            <result property="canback" column="canBack" jdbcType="INTEGER"/>
            <result property="backjdid" column="BackjdID" jdbcType="VARCHAR"/>
            <result property="lookfilejdid" column="LookFileJdID" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,lcjdID,lc_defineID,
        jdmc,nextID,type,
        shgr,GroupID,IsBX,
        FormType,CheckFile,CheckData,
        canBack,BackjdID,LookFileJdID
    </sql>
</mapper>
