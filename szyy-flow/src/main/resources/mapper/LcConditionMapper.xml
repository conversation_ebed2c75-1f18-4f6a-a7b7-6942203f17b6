<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yykj.szyy.flow.mapper.LcConditionMapper">

    <resultMap id="BaseResultMap" type="com.yykj.szyy.flow.entity.LcCondition">
            <id property="id" column="ID" jdbcType="INTEGER"/>
            <result property="pid" column="PID" jdbcType="INTEGER"/>
            <result property="lcDefineid" column="LC_DefineID" jdbcType="INTEGER"/>
            <result property="jdid" column="JDID" jdbcType="INTEGER"/>
            <result property="jdmc" column="JDMC" jdbcType="VARCHAR"/>
            <result property="nextjdid" column="NextJDID" jdbcType="INTEGER"/>
            <result property="nextjdmc" column="NextJDMC" jdbcType="VARCHAR"/>
            <result property="ywb" column="YWB" jdbcType="VARCHAR"/>
            <result property="condition" column="Condition" jdbcType="VARCHAR"/>
            <result property="lConditionCode" column="L_Condition_Code" jdbcType="VARCHAR"/>
            <result property="lCondition" column="L_Condition" jdbcType="VARCHAR"/>
            <result property="zCondition" column="Z_Condition" jdbcType="VARCHAR"/>
            <result property="rCondition" column="R_Condition" jdbcType="VARCHAR"/>
            <result property="fromjscdt" column="FromJScdt" jdbcType="VARCHAR"/>
            <result property="isbx" column="ISBX" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,PID,LC_DefineID,
        JDID,JDMC,NextJDID,
        NextJDMC,YWB,Condition,
        L_Condition_Code,L_Condition,Z_Condition,
        R_Condition,FromJScdt,ISBX
    </sql>
</mapper>
