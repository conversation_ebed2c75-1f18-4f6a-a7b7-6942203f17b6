{
  "properties" : { },
  "id" : "1dab7465724043c091d50b8f7a789859",
  "script" : null,
  "groupId" : "f1aef5492bc64227805d3bf27e341f16",
  "name" : "5.删除流程阶段",
  "createTime" : 1674795032117,
  "updateTime" : 1673418267475,
  "lock" : "0",
  "createBy" : "errorone",
  "updateBy" : null,
  "path" : "/delJd",
  "method" : "GET",
  "parameters" : [ {
    "name" : "id",
    "value" : "4419",
    "description" : "",
    "required" : false,
    "dataType" : "String",
    "type" : null,
    "defaultValue" : null,
    "validateType" : null,
    "error" : null,
    "expression" : null,
    "children" : null
  } ],
  "options" : [ ],
  "requestBody" : "{\n\n}",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : "{\n    \"code\": 500,\n    \"message\": \"数据已过期\"\n}",
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : {
    "name" : "",
    "value" : "",
    "description" : "",
    "required" : false,
    "dataType" : "Object",
    "type" : null,
    "defaultValue" : null,
    "validateType" : "",
    "error" : "",
    "expression" : "",
    "children" : [ {
      "name" : "code",
      "value" : "500",
      "description" : "",
      "required" : false,
      "dataType" : "Integer",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "message",
      "value" : "数据已过期",
      "description" : "",
      "required" : false,
      "dataType" : "String",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    } ]
  }
}
================================
import "@/error" as error;
//检查数据是否存在
const count = db.table("Lcjd").where().eq("id",id).count()
if (count>0){
    return db.table("Lcjd").primary("id").where().eq("id",id).delete()==1?'删除成功!':'删除失败!'
}else{
    return error('数据不存在', 500)
}
