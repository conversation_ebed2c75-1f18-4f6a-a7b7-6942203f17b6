{
  "properties" : { },
  "id" : "518e056e6a4b40c5bbb7e08006da9ed0",
  "script" : null,
  "groupId" : "f1aef5492bc64227805d3bf27e341f16",
  "name" : "1.获取流程阶段",
  "createTime" : 1674795032110,
  "updateTime" : 1672911956662,
  "lock" : "0",
  "createBy" : "errorone",
  "updateBy" : null,
  "path" : "/getJds",
  "method" : "GET",
  "parameters" : [ {
    "name" : "lcId",
    "value" : "9102",
    "description" : "流程ID",
    "required" : true,
    "dataType" : "Integer",
    "type" : null,
    "defaultValue" : null,
    "validateType" : null,
    "error" : "请选择流程",
    "expression" : null,
    "children" : null
  } ],
  "options" : [ ],
  "requestBody" : "{\n\n}",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : "{\n    \"code\": 1,\n    \"message\": \"success\",\n    \"data\": [{\n        \"id\": 408,\n        \"lcjdid\": 910201,\n        \"lcDefineid\": 9102,\n        \"jdmc\": \"用印申请\",\n        \"nextid\": 910202,\n        \"type\": 1,\n        \"shgr\": null,\n        \"groupid\": null,\n        \"isbx\": null,\n        \"formtype\": null,\n        \"checkfile\": null,\n        \"checkdata\": null,\n        \"canback\": null,\n        \"backjdid\": null,\n        \"lookfilejdid\": null\n    }, {\n        \"id\": 409,\n        \"lcjdid\": 910202,\n        \"lcDefineid\": 9102,\n        \"jdmc\": \"主管部门审核\",\n        \"nextid\": 910203,\n        \"type\": 2,\n        \"shgr\": null,\n        \"groupid\": null,\n        \"isbx\": null,\n        \"formtype\": null,\n        \"checkfile\": null,\n        \"checkdata\": null,\n        \"canback\": null,\n        \"backjdid\": \"1|2\",\n        \"lookfilejdid\": null\n    }, {\n        \"id\": 410,\n        \"lcjdid\": 910203,\n        \"lcDefineid\": 9102,\n        \"jdmc\": \"综合管理部意见\",\n        \"nextid\": 0,\n        \"type\": 3,\n        \"shgr\": null,\n        \"groupid\": null,\n        \"isbx\": null,\n        \"formtype\": null,\n        \"checkfile\": null,\n        \"checkdata\": null,\n        \"canback\": null,\n        \"backjdid\": null,\n        \"lookfilejdid\": null\n    }, {\n        \"id\": 411,\n        \"lcjdid\": 910206,\n        \"lcDefineid\": 9102,\n        \"jdmc\": \"总公司审核\",\n        \"nextid\": 0,\n        \"type\": 3,\n        \"shgr\": null,\n        \"groupid\": null,\n        \"isbx\": null,\n        \"formtype\": null,\n        \"checkfile\": null,\n        \"checkdata\": null,\n        \"canback\": null,\n        \"backjdid\": null,\n        \"lookfilejdid\": null\n    }],\n    \"timestamp\": 1671704725196,\n    \"executeTime\": 10\n}",
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : {
    "name" : "",
    "value" : "",
    "description" : "",
    "required" : false,
    "dataType" : "Object",
    "type" : null,
    "defaultValue" : null,
    "validateType" : "",
    "error" : "",
    "expression" : "",
    "children" : [ {
      "name" : "code",
      "value" : "1",
      "description" : "",
      "required" : false,
      "dataType" : "Integer",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "message",
      "value" : "success",
      "description" : "",
      "required" : false,
      "dataType" : "String",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "data",
      "value" : "",
      "description" : "",
      "required" : false,
      "dataType" : "Array",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ {
        "name" : "id",
        "value" : "",
        "description" : "",
        "required" : false,
        "dataType" : "Object",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ {
          "name" : "id",
          "value" : "408",
          "description" : "",
          "required" : false,
          "dataType" : "Integer",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        }, {
          "name" : "lcjdid",
          "value" : "910201",
          "description" : "",
          "required" : false,
          "dataType" : "Integer",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        }, {
          "name" : "lcDefineid",
          "value" : "9102",
          "description" : "",
          "required" : false,
          "dataType" : "Integer",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        }, {
          "name" : "jdmc",
          "value" : "用印申请",
          "description" : "",
          "required" : false,
          "dataType" : "String",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        }, {
          "name" : "nextid",
          "value" : "910202",
          "description" : "",
          "required" : false,
          "dataType" : "Integer",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        }, {
          "name" : "type",
          "value" : "1",
          "description" : "",
          "required" : false,
          "dataType" : "Integer",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        }, {
          "name" : "shgr",
          "value" : "null",
          "description" : "",
          "required" : false,
          "dataType" : "Object",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        }, {
          "name" : "groupid",
          "value" : "null",
          "description" : "",
          "required" : false,
          "dataType" : "Object",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        }, {
          "name" : "isbx",
          "value" : "null",
          "description" : "",
          "required" : false,
          "dataType" : "Object",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        }, {
          "name" : "formtype",
          "value" : "null",
          "description" : "",
          "required" : false,
          "dataType" : "Object",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        }, {
          "name" : "checkfile",
          "value" : "null",
          "description" : "",
          "required" : false,
          "dataType" : "Object",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        }, {
          "name" : "checkdata",
          "value" : "null",
          "description" : "",
          "required" : false,
          "dataType" : "Object",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        }, {
          "name" : "canback",
          "value" : "null",
          "description" : "",
          "required" : false,
          "dataType" : "Object",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        }, {
          "name" : "backjdid",
          "value" : "null",
          "description" : "",
          "required" : false,
          "dataType" : "Object",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        }, {
          "name" : "lookfilejdid",
          "value" : "null",
          "description" : "",
          "required" : false,
          "dataType" : "Object",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        } ]
      } ]
    }, {
      "name" : "timestamp",
      "value" : "1671704725196",
      "description" : "",
      "required" : false,
      "dataType" : "Long",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "executeTime",
      "value" : "10",
      "description" : "",
      "required" : false,
      "dataType" : "Integer",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    } ]
  }
}
================================
return db.table("Lcjd").where().eq("lcDefineid",lcId).orderBy("type").select();