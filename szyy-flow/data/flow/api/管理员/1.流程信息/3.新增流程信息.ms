{
  "properties" : { },
  "id" : "7e85fea860bd4f059e51d3928d68b88a",
  "script" : null,
  "groupId" : "82b7876d102c4840a7c79ba559aba5d3",
  "name" : "3.新增流程信息",
  "createTime" : 1674795032105,
  "updateTime" : 1673334616598,
  "lock" : "0",
  "createBy" : "errorone",
  "updateBy" : null,
  "path" : "/addFlow",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "{\n    \"lcid\": 9102,\n    \"lcname\": \"用印申请流程\",\n    \"ywb\": \"V_Yyjyqb_LC|id|lc_jdmc\",\n    \"ywurl\": \"../../Page/Yyjyqb_LC/Yyjyqb_LCSchdeuleQueue.js\",\n    \"xszd\": \"NULL\",\n    \"isuse\": 0,\n    \"appUrl\": null,\n    \"appYwb\": \"V_Yyjyqb_LC:id:case lc_jdid when 910202 then applicant else groupname end\"\n}",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : "{\n    \"code\": 1,\n    \"message\": \"success\",\n    \"data\": \"更新成功!\",\n    \"timestamp\": 1671708386478,\n    \"executeTime\": 16\n}",
  "description" : null,
  "requestBodyDefinition" : {
    "name" : "",
    "value" : "",
    "description" : "",
    "required" : false,
    "dataType" : "Object",
    "type" : null,
    "defaultValue" : null,
    "validateType" : "",
    "error" : "",
    "expression" : "",
    "children" : [ {
      "name" : "lcid",
      "value" : "9102",
      "description" : "",
      "required" : true,
      "dataType" : "Integer",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "lcname",
      "value" : "用印申请流程",
      "description" : "",
      "required" : true,
      "dataType" : "String",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "ywb",
      "value" : "V_Yyjyqb_LC|id|lc_jdmc",
      "description" : "",
      "required" : true,
      "dataType" : "String",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "ywurl",
      "value" : "../../Page/Yyjyqb_LC/Yyjyqb_LCSchdeuleQueue.js",
      "description" : "",
      "required" : false,
      "dataType" : "String",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "xszd",
      "value" : "NULL",
      "description" : "",
      "required" : false,
      "dataType" : "String",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "isuse",
      "value" : "0",
      "description" : "",
      "required" : false,
      "dataType" : "Integer",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "appUrl",
      "value" : "null",
      "description" : "",
      "required" : false,
      "dataType" : "Object",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "appYwb",
      "value" : "V_Yyjyqb_LC:id:case lc_jdid when 910202 then applicant else groupname end",
      "description" : "",
      "required" : false,
      "dataType" : "String",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    } ]
  },
  "responseBodyDefinition" : {
    "name" : "",
    "value" : "",
    "description" : "",
    "required" : false,
    "dataType" : "Object",
    "type" : null,
    "defaultValue" : null,
    "validateType" : "",
    "error" : "",
    "expression" : "",
    "children" : [ {
      "name" : "code",
      "value" : "1",
      "description" : "",
      "required" : false,
      "dataType" : "Integer",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "message",
      "value" : "success",
      "description" : "",
      "required" : false,
      "dataType" : "String",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "data",
      "value" : "更新成功!",
      "description" : "",
      "required" : false,
      "dataType" : "Object",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "timestamp",
      "value" : "1671708386478",
      "description" : "",
      "required" : false,
      "dataType" : "Long",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "executeTime",
      "value" : "16",
      "description" : "",
      "required" : false,
      "dataType" : "Integer",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    } ]
  }
}
================================
import "@/error" as error;
//检查数据是否存在
const lcid = body.lcid;

const count = db.table("Lcdefine").where().eq("lcid", lcid).count()
if (count == 0) {
    return db.table("Lcdefine").primary("id").insert(body) > 0 ? '更新成功!' : '更新失败!'
} else {
    return error('数据已过期', 500)
}