{
  "properties" : { },
  "id" : "981ab3ac894b4afabd49f1760c0cc499",
  "script" : null,
  "groupId" : "82b7876d102c4840a7c79ba559aba5d3",
  "name" : "1.获取流程信息",
  "createTime" : null,
  "updateTime" : 1675758365915,
  "lock" : "0",
  "createBy" : "errorone",
  "updateBy" : "errorone",
  "path" : "/getFlows",
  "method" : "POST",
  "parameters" : [ {
    "name" : "current",
    "value" : "",
    "description" : "当前页",
    "required" : false,
    "dataType" : "Integer",
    "type" : null,
    "defaultValue" : "1",
    "validateType" : null,
    "error" : null,
    "expression" : null,
    "children" : null
  }, {
    "name" : "size",
    "value" : "",
    "description" : "每页数",
    "required" : false,
    "dataType" : "Integer",
    "type" : null,
    "defaultValue" : "10",
    "validateType" : null,
    "error" : null,
    "expression" : null,
    "children" : null
  } ],
  "options" : [ {
    "name" : "anonymous",
    "value" : "true",
    "description" : "该接口需要不登录也可访问",
    "required" : false,
    "dataType" : "String",
    "type" : null,
    "defaultValue" : null,
    "validateType" : null,
    "error" : null,
    "expression" : null,
    "children" : null
  } ],
  "requestBody" : "{\n\n}",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : "{\n    \"code\": 200,\n    \"message\": \"success\",\n    \"result\": {\n        \"total\": 69,\n        \"list\": [{\n            \"RowNumber\": 1,\n            \"id\": 3,\n            \"lcid\": 1101,\n            \"lcname\": \"业扩流程\",\n            \"ywb\": \"T_BE_PROJECT|ID|dbo.FUNC_LC_DBINFO(&1,&2,&3)\",\n            \"ywurl\": \"../../Page/Project/NT_BE_PROJECTFlowManageQueue.js\",\n            \"xszd\": \"v$T_BE_PROJECTByLC\",\n            \"isuse\": 0,\n            \"appUrl\": null,\n            \"appYwb\": \"t_be_project:ID:jdmc+'-'+pro_name\"\n        }, {\n            \"RowNumber\": 2,\n            \"id\": 4,\n            \"lcid\": 1102,\n            \"lcname\": \"局工程\",\n            \"ywb\": \"JGC_PROJ_INFO|PROJ_ID|dbo.FUNC_LC_DBINFO(&1,&2,&3)\",\n            \"ywurl\": \"../../Page/JGCManage/NDBLC_ManageQueue.js\",\n            \"xszd\": \"JGC_PROJ_INFO\",\n            \"isuse\": 0,\n            \"appUrl\": null,\n            \"appYwb\": null\n        }, {\n            \"RowNumber\": 3,\n            \"id\": 5,\n            \"lcid\": 3101,\n            \"lcname\": \"35KV以上流程\",\n            \"ywb\": null,\n            \"ywurl\": null,\n            \"xszd\": \"v$GY_T_BE_PROJECTByLC\",\n            \"isuse\": 0,\n            \"appUrl\": null,\n            \"appYwb\": null\n        }, {\n            \"RowNumber\": 4,\n            \"id\": 6,\n            \"lcid\": 1202,\n            \"lcname\": \"合同流程\",\n            \"ywb\": \"v$T_HTGL:ID:'合同编号['+HTBH+']'+HTName\",\n            \"ywurl\": \"../../Page/HTManage/NT_HTFlowManageQueue.js\",\n            \"xszd\": \"v$T_HTGL\",\n            \"isuse\": 0,\n            \"appUrl\": \"/pages/hetong/hetong\",\n            \"appYwb\": null\n        }, {\n            \"RowNumber\": 5,\n            \"id\": 18,\n            \"lcid\": 9102,\n            \"lcname\": \"用印申请流程\",\n            \"ywb\": \"V_Yyjyqb_LC|id|lc_jdmc\",\n            \"ywurl\": \"../../Page/Yyjyqb_LC/Yyjyqb_LCSchdeuleQueue.js\",\n            \"xszd\": \"NULL\",\n            \"isuse\": 0,\n            \"appUrl\": null,\n            \"appYwb\": \"V_Yyjyqb_LC:id:case lc_jdid when 910202 then applicant else groupname end\"\n        }, {\n            \"RowNumber\": 6,\n            \"id\": 19,\n            \"lcid\": 9402,\n            \"lcname\": \"综合签报流程\",\n            \"ywb\": \"V_Gsxgsxspd_LC|ID|lc_jdmc\",\n            \"ywurl\": \"../../Page/Gsxgsxspd_LC/Gsxgsxspd_LCSchdeuleQueue.js\",\n            \"xszd\": \"NULL\",\n            \"isuse\": 0,\n            \"appUrl\": null,\n            \"appYwb\": null\n        }, {\n            \"RowNumber\": 7,\n            \"id\": 29,\n            \"lcid\": 9005,\n            \"lcname\": \"固定资产申请流程\",\n            \"ywb\": \"SELECT Title,Maker from GDZC where ID =\",\n            \"ywurl\": \"../../Page/GdzcManage/T_GdzcSHManageQueue.js\",\n            \"xszd\": \"V_gdzx_WorkFlow\",\n            \"isuse\": null,\n            \"appUrl\": null,\n            \"appYwb\": null\n        }, {\n            \"RowNumber\": 8,\n            \"id\": 50,\n            \"lcid\": 5201,\n            \"lcname\": \"分包学习-学习资源发布审批\",\n            \"ywb\": \"STUDY_RESOURCE|ID|NAME\",\n            \"ywurl\": \"../../Page/FBXXManage/NDBLC_ManageQueue.js\",\n            \"xszd\": \"\",\n            \"isuse\": 0,\n            \"appUrl\": null,\n            \"appYwb\": null\n        }, {\n            \"RowNumber\": 9,\n            \"id\": 53,\n            \"lcid\": 9702,\n            \"lcname\": \"基建项目流程\",\n            \"ywb\": \"V_JJXMBA|ID|XMMC+^-^+lc_jdmc\",\n            \"ywurl\": \"../../Page/JJXMBA/JJXMBAPendingManageQueue.js\",\n            \"xszd\": null,\n            \"isuse\": null,\n            \"appUrl\": null,\n            \"appYwb\": null\n        }, {\n            \"RowNumber\": 10,\n            \"id\": 54,\n            \"lcid\": 5205,\n            \"lcname\": \"申请上会-上会审批流程\",\n            \"ywb\": \"SHS_MAIN|ID|^上会申请单^+SHS_BH\",\n            \"ywurl\": \"../../Page/SHSManage/NDBLC_ManageQueue.js\",\n            \"xszd\": \"\",\n            \"isuse\": 0,\n            \"appUrl\": null,\n            \"appYwb\": null\n        }]\n    },\n    \"success\": true,\n    \"timestamp\": 1675753789722\n}",
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : {
    "name" : "",
    "value" : "",
    "description" : "",
    "required" : false,
    "dataType" : "Object",
    "type" : null,
    "defaultValue" : null,
    "validateType" : "",
    "error" : "",
    "expression" : "",
    "children" : [ {
      "name" : "code",
      "value" : "200",
      "description" : "",
      "required" : false,
      "dataType" : "Integer",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "message",
      "value" : "success",
      "description" : "",
      "required" : false,
      "dataType" : "String",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "result",
      "value" : "",
      "description" : "",
      "required" : false,
      "dataType" : "Object",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ {
        "name" : "total",
        "value" : "69",
        "description" : "",
        "required" : false,
        "dataType" : "Integer",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      }, {
        "name" : "list",
        "value" : "",
        "description" : "",
        "required" : false,
        "dataType" : "Array",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ {
          "name" : "",
          "value" : "",
          "description" : "",
          "required" : false,
          "dataType" : "Object",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ {
            "name" : "RowNumber",
            "value" : "1",
            "description" : "",
            "required" : false,
            "dataType" : "Integer",
            "type" : null,
            "defaultValue" : null,
            "validateType" : "",
            "error" : "",
            "expression" : "",
            "children" : [ ]
          }, {
            "name" : "id",
            "value" : "3",
            "description" : "",
            "required" : false,
            "dataType" : "Integer",
            "type" : null,
            "defaultValue" : null,
            "validateType" : "",
            "error" : "",
            "expression" : "",
            "children" : [ ]
          }, {
            "name" : "lcid",
            "value" : "1101",
            "description" : "",
            "required" : false,
            "dataType" : "Integer",
            "type" : null,
            "defaultValue" : null,
            "validateType" : "",
            "error" : "",
            "expression" : "",
            "children" : [ ]
          }, {
            "name" : "lcname",
            "value" : "业扩流程",
            "description" : "",
            "required" : false,
            "dataType" : "String",
            "type" : null,
            "defaultValue" : null,
            "validateType" : "",
            "error" : "",
            "expression" : "",
            "children" : [ ]
          }, {
            "name" : "ywb",
            "value" : "T_BE_PROJECT|ID|dbo.FUNC_LC_DBINFO(&1,&2,&3)",
            "description" : "",
            "required" : false,
            "dataType" : "String",
            "type" : null,
            "defaultValue" : null,
            "validateType" : "",
            "error" : "",
            "expression" : "",
            "children" : [ ]
          }, {
            "name" : "ywurl",
            "value" : "../../Page/Project/NT_BE_PROJECTFlowManageQueue.js",
            "description" : "",
            "required" : false,
            "dataType" : "String",
            "type" : null,
            "defaultValue" : null,
            "validateType" : "",
            "error" : "",
            "expression" : "",
            "children" : [ ]
          }, {
            "name" : "xszd",
            "value" : "v$T_BE_PROJECTByLC",
            "description" : "",
            "required" : false,
            "dataType" : "String",
            "type" : null,
            "defaultValue" : null,
            "validateType" : "",
            "error" : "",
            "expression" : "",
            "children" : [ ]
          }, {
            "name" : "isuse",
            "value" : "0",
            "description" : "",
            "required" : false,
            "dataType" : "Integer",
            "type" : null,
            "defaultValue" : null,
            "validateType" : "",
            "error" : "",
            "expression" : "",
            "children" : [ ]
          }, {
            "name" : "appUrl",
            "value" : "null",
            "description" : "",
            "required" : false,
            "dataType" : "Object",
            "type" : null,
            "defaultValue" : null,
            "validateType" : "",
            "error" : "",
            "expression" : "",
            "children" : [ ]
          }, {
            "name" : "appYwb",
            "value" : "t_be_project:ID:jdmc+'-'+pro_name",
            "description" : "",
            "required" : false,
            "dataType" : "String",
            "type" : null,
            "defaultValue" : null,
            "validateType" : "",
            "error" : "",
            "expression" : "",
            "children" : [ ]
          } ]
        } ]
      } ]
    }, {
      "name" : "success",
      "value" : "true",
      "description" : "",
      "required" : false,
      "dataType" : "Boolean",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "timestamp",
      "value" : "1675753789722",
      "description" : "",
      "required" : false,
      "dataType" : "Long",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    } ]
  }
}
================================
return db.table("Lcdefine").orderBy("id").page(size,(current-1)*size);