package com.soft.gcc.base.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 指南针软件系统-版本控制表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("CPS_VER_CTRL")
@ApiModel(value="CPS_VER_CTRL对象", description="指南针软件系统-版本控制表")
public class CPS_VER_CTRL extends Model<CPS_VER_CTRL> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "当前版本")
    @JSONField(name="VC_VER")
    private Double VC_VER;


    @Override
    public Serializable pkVal() {
        return this.VC_VER;
    }

}
