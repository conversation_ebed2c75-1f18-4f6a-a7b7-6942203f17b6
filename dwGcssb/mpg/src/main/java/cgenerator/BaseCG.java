package cgenerator;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import org.apache.logging.log4j.core.config.ConfigurationSource;
import java.io.FileInputStream;

import static org.apache.logging.log4j.core.config.Configurator.*;

public class BaseCG {

    public static void main(String[] args) {
        String rootpath = System.getProperty("user.dir");
        String realpath=rootpath+"\\mpg\\src\\main\\resources\\log4j2.xml";

        ConfigurationSource source=null;
        try
        {
            source= new ConfigurationSource(new FileInputStream(realpath));
            initialize(null, source);

            // 代码生成器
            AutoGenerator mpg = new AutoGenerator();

            // 全局配置
            GlobalConfig gc = new GlobalConfig();

            //生成文件的输出目录
            gc.setOutputDir(rootpath+"/src/main/java/");
            gc.setAuthor("qidefang");
            gc.setOpen(false);
            gc.setFileOverride(false);
            gc.setActiveRecord(true);
            gc.setSwagger2(true);
            mpg.setGlobalConfig(gc);

            DataSourceConfig dsc = new DataSourceConfig();
            dsc.setUrl("********************************************************;");
            dsc.setDriverName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
            dsc.setUsername("sa");
            dsc.setDbType(DbType.SQL_SERVER);
            dsc.setPassword("sa");
            mpg.setDataSource(dsc);

            // 包配置
            PackageConfig pc = new PackageConfig();
            pc.setParent("com.soft.gcc.base");
            pc.setEntity("entity");
            pc.setService("service");
            pc.setServiceImpl("service.impl");
            pc.setController("controller");
            pc.setMapper("mapper");
            pc.setXml("xml");
            mpg.setPackageInfo(pc);

            // 配置模板
            TemplateConfig templateConfig = new TemplateConfig();
            //templateConfig.setMapper(null);
            //templateConfig.setService(null);
            templateConfig.setController(null);
            //templateConfig.setServiceImpl(null);
            //templateConfig.setXml(null);
            mpg.setTemplate(templateConfig);

            String [] tables=new String[]{
                "CPS_T_GRPT_INFO", "CPS_T_GRPT_SUPP", "CPS_T_GRPT_TYPE","CPS_VER_CTRL","NFT_LOGIN_UC","NFT_ModuleGroup",
                "NFT_ModuleQydz","ApplicationLog","ApplicationLog_YW",
                "CPS_T_HAND_LOG",
                "sysloginfo","sysoperlog", "sysjob","sysjoblog",
                "CPS_V_GRPT_INFO"
            };

            // 策略配置X
            StrategyConfig strategy = new StrategyConfig();
            strategy.setNaming(NamingStrategy.no_change);
            strategy.setColumnNaming(NamingStrategy.no_change);
            strategy.setCapitalMode(false);
            strategy.setEntityLombokModel(true);
            strategy.setRestControllerStyle(false);
            strategy.setEntityColumnConstant(false);
            strategy.setSkipView(false);
            strategy.setInclude(tables);
            strategy.setEntityLombokModel(true);
            strategy.setEntityTableFieldAnnotationEnable(true);
            strategy.setControllerMappingHyphenStyle(false);
            strategy.setNameConvert(new CNameConvert());
            mpg.setStrategy(strategy);

            FreemarkerTemplateEngine freemarkerTemplateEngine=new FreemarkerTemplateEngine();
            freemarkerTemplateEngine.templateFilePath("/templates/");
            //设置模板引擎
            mpg.setTemplateEngine(freemarkerTemplateEngine);
            mpg.execute();
        }catch(Exception Ex)
        {
            String exmsg=Ex.getMessage();
        }
    }
}
