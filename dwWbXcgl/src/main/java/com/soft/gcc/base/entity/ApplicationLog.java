package com.soft.gcc.base.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ApplicationLog")
@ApiModel(value="ApplicationLog对象", description="")
public class ApplicationLog extends Model<ApplicationLog> {

    private static final long serialVersionUID = 1L;

    @JSONField(name="Guid")
    private String Guid;

    @JSONField(name="OperateModule")
    private String OperateModule;

    @JSONField(name="OperateFunction")
    private String OperateFunction;

    @JSONField(name="OperatorName")
    private String OperatorName;

    @JSONField(name="OperateDate")
    private LocalDateTime OperateDate;

    @JSONField(name="OperateDetail")
    private String OperateDetail;


    @Override
    protected Serializable pkVal() {
        return null;
    }

}
