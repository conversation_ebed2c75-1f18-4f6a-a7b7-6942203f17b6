package com.soft.gcc.base.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * sysoperlog
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sysoperlog")
@ApiModel(value="sysoperlog对象", description="sysoperlog")
public class sysoperlog extends Model<sysoperlog> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "记录编号")
    @TableId(value = "LogId", type = IdType.AUTO)
    @JSONField(name="LogId")
    private Long LogId;

    @ApiModelProperty(value = "所属用户")
    @JSONField(name="OperUser")
    private String OperUser;

    @ApiModelProperty(value = "所属部门")
    @JSONField(name="DeptName")
    private String DeptName;

    @ApiModelProperty(value = "请求IP")
    @JSONField(name="OperIp")
    private String OperIp;

    @ApiModelProperty(value = "请求地址")
    @JSONField(name="OperUrl")
    private String OperUrl;

    @ApiModelProperty(value = "请求底单")
    @JSONField(name="OperLocation")
    private String OperLocation;

    @ApiModelProperty(value = "请求参数")
    @JSONField(name="OperParam")
    private String OperParam;

    @ApiModelProperty(value = "请求方法")
    @JSONField(name="Method")
    private String Method;

    @ApiModelProperty(value = "请求方式")
    @JSONField(name="RequestMethod")
    private String RequestMethod;

    @ApiModelProperty(value = "请求类别0=其他，1=后台，2=手机")
    @JSONField(name="OperatorType")
    private Integer OperatorType;

    @ApiModelProperty(value = "标题")
    @JSONField(name="Title")
    private String Title;

    @ApiModelProperty(value = "状态")
    @JSONField(name="Status")
    private Integer Status;

    @ApiModelProperty(value = "json结果")
    @JSONField(name="JsonResult")
    private String JsonResult;

    @ApiModelProperty(value = "错误信息")
    @JSONField(name="ErrorMsg")
    private String ErrorMsg;

    @ApiModelProperty(value = "业务类型：0=其它,1=新增,2=修改,3=删除,4=授权,5=导出,6=导入,7=强退,8=生成代码,9=清空数据")
    @JSONField(name="BusinessType")
    private Integer BusinessType;

    @ApiModelProperty(value = "业务类型数组")
    @JSONField(name="BusinessTypes")
    private String BusinessTypes;


    @Override
    protected Serializable pkVal() {
        return this.LogId;
    }

}
