package com.soft.gcc.xtbg.xcgl.dto;

import lombok.Data;

import java.util.Date;

/**
 * 行程打卡照片汇总 dto
 */
@Data
public class GcjlTXcdkzphzDto extends PageBaseEntity {
    private Integer deptId;

    private String userName;
    /**
     * 行程开始日期 改string 防止前端获取到的日期格式不正确
     */
//    @JsonFormat( pattern = "yyyy-MM-dd")
//    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String planStartDate;

    /**
     * 行程结束日期
     */
//    @JsonFormat( pattern = "yyyy-MM-dd")
//    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String planEndDate;

    private Integer approvingState;


    private Integer performState;

    private String deptFullPath;

}
