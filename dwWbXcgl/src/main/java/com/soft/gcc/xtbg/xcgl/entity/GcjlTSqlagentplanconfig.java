package com.soft.gcc.xtbg.xcgl.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.util.*;
import lombok.Data;

/**
* <AUTHOR>
* 工程监理_Sql计划任务参数配置表
* @TableName GCJL_T_SqlAgentPlanConfig
*/
@TableName(value ="GCJL_T_SqlAgentPlanConfig")
@Data
public class GcjlTSqlagentplanconfig implements Serializable {

    /**
    * 主键：
    */

    @TableId(value = "Id", type = IdType.AUTO)
    @JSONField(name = "Id")
    private Integer Id;

    /**
    * 参数代码
    */
    @TableField(value = "Code")
    @JSONField(name = "Code")

    private String Code;
    /**
    * 创建时间
    */
    @TableField(value = "CreateTime")
    @JSONField(name = "CreateTime")
    @JsonFormat( pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date CreateTime;
    /**
    * 参数值
    */
    @TableField(value = "Value")
    @JSONField(name = "Value")
    @JsonFormat( pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date Value;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
