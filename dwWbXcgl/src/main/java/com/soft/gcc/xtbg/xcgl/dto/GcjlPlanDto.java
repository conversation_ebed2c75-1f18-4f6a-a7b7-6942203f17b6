package com.soft.gcc.xtbg.xcgl.dto;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTLc;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @createDate 2023/7/27
 */
@Data
public class GcjlPlanDto extends GcjlTLc {
    /**
     * 流程Id（流程表主键id）
     */
    private Integer LcId;

    /**
     * 监理计划
     */
    private String PlanName;

    /**
     * 排序号
     */
    private Integer Sort;

    /**
     * 进入时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date ArrivedTime;
    private String ArrivedTimeStr;

    /**
     * 离开时间
     */
    private Date DepartTime;
    /**
     * 纬度
     */
    private BigDecimal Latitude;
    /**
     * 经度
     */
    private BigDecimal Longitude;

    private String ArrivedLocationName;

    private Date PlanTime;

    private String approveUserName;
    private Integer ApproveUserId;

    /**
     * 打卡图片id
     */
    private Integer clockImgId;
    /**
     * 打卡图片地址
     */
    private String clockImgFileUrl;
    /**
     * 里程
     */
    private String mileage;
    /**
     * 费用
     */
    private String cost;

    private String FilePath;

    private String longitudeLatitude;

    /**
     * 照片
     */
    private String SubTName;

    private String FileName;
    /**
     * 确认状态（0未提交、1审批中、2已驳回、3确认通过）
     */
    private Integer NotarizeState;
    /**
     * 审核状态（0 未提交、1 审核中、2 已驳回、3 已审核、4已过期、5已作废
     */
    private Integer ApprovingState;
    /**
     * 申请延期天数：行程确认提交日期-行程计划日期
     */
    private Integer ApplyPostponeDays;
    /**
     * 审批延期天数：行程确认审批提交日期- 一级审批人审批结束日期
     */
    private Integer ApprovePostponeDays;

    private Integer IsGoCompany;
    private String IsGoCompanyStr;

    private Integer PointState;
    private String PointStateStr;


    /**   **/
    private String planTimeStr;
    private String DeptName;
    private String UserName;
    private String approvingStateDescription;
    private String notarizeStateDescription;
    private String performStateDescription;

}
