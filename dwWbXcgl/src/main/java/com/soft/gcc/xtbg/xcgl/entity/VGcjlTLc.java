package com.soft.gcc.xtbg.xcgl.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import lombok.Data;

/**
* <AUTHOR>
*
* @TableName V_GCJL_T_LC
*/
@TableName(value ="V_GCJL_T_LC")
@Data
public class VGcjlTLc implements Serializable {


    /**
    *
    */
    @TableField(value = "Id")
    @JSONField(name = "Id")

    private Integer Id;
    /**
    *
    */
    @TableField(value = "BizNo")
    @JSONField(name = "BizNo")

    private String BizNo;
    /**
    *
    */
    @TableField(value = "UserId")
    @JSONField(name = "UserId")

    private Integer UserId;
    /**
    *
    */
    @TableField(value = "UserName")
    @JSONField(name = "UserName")

    private String UserName;
    /**
    *
    */
    @TableField(value = "UserFullPath")
    @JSONField(name = "UserFullPath")

    private String UserFullPath;
    /**
    *
    */
    @TableField(value = "PlanTime")
    @JSONField(name = "PlanTime")
    @JsonFormat( pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date PlanTime;
    /**
    *
    */
    @TableField(value = "Note")
    @JSONField(name = "Note")

    private String Note;
    /**
    *
    */
    @TableField(value = "CreateTime")
    @JSONField(name = "CreateTime")
    @JsonFormat( pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date CreateTime;
    /**
    *
    */
    @TableField(value = "PlanMileage")
    @JSONField(name = "PlanMileage")

    private BigDecimal PlanMileage;
    /**
    *
    */
    @TableField(value = "PlanCost")
    @JSONField(name = "PlanCost")

    private BigDecimal PlanCost;
    /**
    *
    */
    @TableField(value = "PerformMileage")
    @JSONField(name = "PerformMileage")

    private BigDecimal PerformMileage;
    /**
    *
    */
    @TableField(value = "PerformCost")
    @JSONField(name = "PerformCost")

    private BigDecimal PerformCost;
    /**
    *
    */
    @TableField(value = "BizType")
    @JSONField(name = "BizType")

    private String BizType;
    /**
    *
    */
    @TableField(value = "ApprovingState")
    @JSONField(name = "ApprovingState")

    private Integer ApprovingState;
    /**
    *
    */
    @TableField(value = "ApproveUserId")
    @JSONField(name = "ApproveUserId")

    private Integer ApproveUserId;
    /**
    *
    */
    @TableField(value = "ApproveNote")
    @JSONField(name = "ApproveNote")

    private String ApproveNote;
    /**
    *
    */
    @TableField(value = "ApproveTime")
    @JSONField(name = "ApproveTime")
    @JsonFormat( pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date ApproveTime;
    /**
    *
    */
    @TableField(value = "ApproveDeptId")
    @JSONField(name = "ApproveDeptId")

    private Integer ApproveDeptId;
    /**
    *
    */
    @TableField(value = "TopDeptId")
    @JSONField(name = "TopDeptId")

    private Integer TopDeptId;
    /**
    *
    */
    @TableField(value = "DeptId")
    @JSONField(name = "DeptId")

    private Integer DeptId;
    /**
    *
    */
    @TableField(value = "DeptName")
    @JSONField(name = "DeptName")

    private String DeptName;
    /**
    *
    */
    @TableField(value = "Phone")
    @JSONField(name = "Phone")

    private String Phone;
    /**
    *
    */
    @TableField(value = "PerformState")
    @JSONField(name = "PerformState")

    private Integer PerformState;
    /**
    *
    */
    @TableField(value = "Lc_defineID")
    @JSONField(name = "Lc_defineID")

    private Integer Lc_defineID;
    /**
    *
    */
    @TableField(value = "Lc_Name")
    @JSONField(name = "Lc_Name")

    private String Lc_Name;
    /**
    *
    */
    @TableField(value = "ywID")
    @JSONField(name = "ywID")

    private Integer ywID;
    /**
    *
    */
    @TableField(value = "sendPerson")
    @JSONField(name = "sendPerson")

    private String sendPerson;
    /**
    *
    */
    @TableField(value = "sendPersonZgh")
    @JSONField(name = "sendPersonZgh")

    private String sendPersonZgh;
    /**
    *
    */
    @TableField(value = "AllPersonZgh")
    @JSONField(name = "AllPersonZgh")

    private String AllPersonZgh;
    /**
    *
    */
    @TableField(value = "isMany")
    @JSONField(name = "isMany")

    private Integer isMany;
    /**
    *
    */
    @TableField(value = "lc_jdmc")
    @JSONField(name = "lc_jdmc")

    private String lc_jdmc;
    /**
    *
    */
    @TableField(value = "lc_jdid")
    @JSONField(name = "lc_jdid")

    private Integer lc_jdid;
    /**
    *
    */
    @TableField(value = "lc_isback")
    @JSONField(name = "lc_isback")

    private Integer lc_isback;
    /**
    *
    */
    @TableField(value = "lc_tojdid")
    @JSONField(name = "lc_tojdid")

    private String lc_tojdid;
    /**
    *
    */
    @TableField(value = "number")
    @JSONField(name = "number")

    private Integer number;
    /**
    *
    */
    @TableField(value = "BXType")
    @JSONField(name = "BXType")

    private String BXType;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
