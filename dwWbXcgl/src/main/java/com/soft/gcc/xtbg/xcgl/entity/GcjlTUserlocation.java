package com.soft.gcc.xtbg.xcgl.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import lombok.Data;

/**
* <AUTHOR>
* 工程监理_用户定位表
* @TableName GCJL_T_UserLocation
*/
@TableName(value ="GCJL_T_UserLocation")
@Data
public class GcjlTUserlocation implements Serializable {

    /**
    * 主键：
    */

    @TableId(value = "Id", type = IdType.AUTO)
    @JSONField(name = "Id")
    private Integer Id;

    /**
    * 对应用户id
    */
    @TableField(value = "UserId")
    @JSONField(name = "UserId")

    private Integer UserId;
    /**
    * 纬度
    */
    @TableField(value = "Latitude")
    @JSONField(name = "Latitude")

    private BigDecimal Latitude;
    /**
    * 经度
    */
    @TableField(value = "Longitude")
    @JSONField(name = "Longitude")

    private BigDecimal Longitude;
    /**
    * 计划行程子表Id
    */
    @TableField(value = "PlanPointId")
    @JSONField(name = "PlanPointId")

    private Integer PlanPointId;
    /**
    * 距离1970-01-01第几个五分钟 用户定位时间切片
    */
    @TableField(value = "MonitorSpan")
    @JSONField(name = "MonitorSpan")

    private Long MonitorSpan;
    /**
    * 创建时间
    */
    @TableField(value = "CreateTime")
    @JSONField(name = "CreateTime")
    @JsonFormat( pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date CreateTime;
    /**
    * 用户定位时间
    */
    @TableField(value = "LocationTime")
    @JSONField(name = "LocationTime")
    @JsonFormat( pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date LocationTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
