package com.soft.gcc.xtbg.xcgl.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import lombok.Data;

/**
* <AUTHOR>
* 工程监理--计划行程子表
* @TableName GCJL_T_Plan
*/
@TableName(value ="GCJL_T_Plan")
@Data
public class GcjlTPlan implements Serializable {

    /**
    * 主键：
    */

    @TableId(value = "Id", type = IdType.AUTO)
    @JSONField(name = "Id")
    private Integer Id;

    /**
    * 流程Id（流程表主键id）
    */
    @TableField(value = "LcId")
    @JSONField(name = "LcId")

    private Integer LcId;
    /**
    * 监理计划
    */
    @TableField(value = "PlanName")
    @JSONField(name = "PlanName")

    private String PlanName;
    /**
    * 纬度
    */
    @TableField(value = "Latitude")
    @JSONField(name = "Latitude")

    private BigDecimal Latitude;
    /**
    * 经度
    */
    @TableField(value = "Longitude")
    @JSONField(name = "Longitude")

    private BigDecimal Longitude;
    /**
    * 里程
    */
    @TableField(value = "Mileage")
    @JSONField(name = "Mileage")

    private BigDecimal Mileage;
    /**
    * 费用
    */
    @TableField(value = "Cost")
    @JSONField(name = "Cost")

    private BigDecimal Cost;
    /**
    * 排序号
    */
    @TableField(value = "Sort")
    @JSONField(name = "Sort")

    private Integer Sort;
    /**
    * 点状态（1 已到 0 未到）
    */
    @TableField(value = "PointState")
    @JSONField(name = "PointState")

    private Integer PointState;
    /**
    * 创建时间
    */
    @TableField(value = "CreateTime")
    @JSONField(name = "CreateTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date CreateTime;
    /**
    * 进入时间
    */
    @TableField(value = "ArrivedTime")
    @JSONField(name = "ArrivedTime")
    @JsonFormat( pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date ArrivedTime;
    /**
    * 离开时间
    */
    @TableField(value = "DepartTime")
    @JSONField(name = "DepartTime")
    @JsonFormat( pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date DepartTime;
    /**
    * 停留时长，分
    */
    @TableField(value = "ResidenceTime")
    @JSONField(name = "ResidenceTime")

    private Integer ResidenceTime;
    /**
    * 所对应的常用工程点id
    */
    @TableField(value = "PlanProjectId")
    @JSONField(name = "PlanProjectId")

    private Integer PlanProjectId;
    /**
    * 是否回到单位标识（0否，1是），用于标识防止多次插入
    */
    @TableField(value = "IsGoCompany")
    @JSONField(name = "IsGoCompany")

    private Integer IsGoCompany;
    /**
    * 位置名称
    */
    @TableField(value = "LocationName")
    @JSONField(name = "LocationName")

    private String LocationName;
    /**
    * 省份
    */
    @TableField(value = "Provinces")
    @JSONField(name = "Provinces")

    private Integer Provinces;
    /**
    * 市
    */
    @TableField(value = "City")
    @JSONField(name = "City")

    private Integer City;
    /**
    * 区
    */
    @TableField(value = "Area")
    @JSONField(name = "Area")

    private Integer Area;
    /**
    * 区域中文
    */
    @TableField(value = "AreaText")
    @JSONField(name = "AreaText")

    private String AreaText;
    /**
    * 打卡时经度
    */
    @TableField(value = "ArrivedLongitude")
    @JSONField(name = "ArrivedLongitude")

    private BigDecimal ArrivedLongitude;
    /**
    * 打卡时纬度
    */
    @TableField(value = "ArrivedLatitude")
    @JSONField(name = "ArrivedLatitude")

    private BigDecimal ArrivedLatitude;
    /**
    * 实际打卡地址
    */
    @TableField(value = "ArrivedLocationName")
    @JSONField(name = "ArrivedLocationName")

    private String ArrivedLocationName;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
