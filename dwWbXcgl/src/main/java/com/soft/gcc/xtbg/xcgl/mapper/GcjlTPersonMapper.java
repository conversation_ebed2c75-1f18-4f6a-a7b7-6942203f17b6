package com.soft.gcc.xtbg.xcgl.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.soft.framework.aspectj.lang.annotation.DataSource;
import com.soft.framework.aspectj.lang.enums.DataSourceType;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTPerson;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【GCJL_T_Person(工程监理--人员表)】的数据库操作Mapper
* @createDate 2023-07-12 10:58:51
* @Entity com.soft.gcc.xtbg.xcgl.entity.GcjlTPerson
*/
public interface GcjlTPersonMapper extends BaseMapper<GcjlTPerson> {
    /**
     *
     * @param page
     * @param deptId  0 就不查
     * @param realName
     * @return
     */
    
    IPage<GcjlTPerson> getPersonList(Page<GcjlTPerson> page,@Param("deptId") Integer deptId,@Param("realName") String realName);


    /**
     *  清理 EndMileage，EndCost，JlId，EndArrivedTime，EndArrivedJlId 设置成null
     * @param id
     * @return
     */
    int cleanEndData(@Param("id") Integer id);

    List<GcjlTPerson> exportExcel(@Param("deptId") Integer deptId,@Param("realName") String realName);


    void updateCostTopLimit(@Param("srcUserId")Integer srcUserId , @Param("CostTopLimit") BigDecimal costTopLimit);


    void batchInsert(List<GcjlTPerson> list);


}




