package com.soft.gcc.xtbg.xcgl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 工程监理--组织架构表
 * @TableName GCJL_T_Department
 */
@Data
@TableName(value ="GCJL_T_Department2")
@Accessors(chain = true)
public class GcjlTDepartment extends XcglBaseEntity implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 父Id
     */
    private Integer parentid;

    /**
     * 部门名称
     */
    private String deptname;

    /**
     * 部门全路径，宁波yy\yinzyongn\yinzjlz
     */
    private String deptfullpath;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 状态(1启用 0 禁用)
     */
    private Integer state;

    /**
     * 创建时间
     */
    private Date createtime;

    /**
     * 排序号
     */
    private Integer sort;

    /**
     * 工程监理范围
     */
    private String addresslimit;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;


    @TableField(exist = false)
    private String GPS;

    @TableField(exist = false)
    private String stateTxt;
}
