package com.soft.gcc.xtbg.xcgl.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.util.*;
import lombok.Data;

/**
* <AUTHOR>
* 工程监理_告警记录表
* @TableName GCJL_T_ReportAlarm
*/
@TableName(value ="GCJL_T_ReportAlarm")
@Data
public class GcjlTReportalarm implements Serializable {

    /**
    * 主键：
    */

    @TableId(value = "Id", type = IdType.AUTO)
    @JSONField(name = "Id")
    private Integer Id;

    /**
    * 部门ID
    */
    @TableField(value = "DeptId")
    @JSONField(name = "DeptId")

    private Integer DeptId;
    /**
    * 告警类型
    */
    @TableField(value = "AlarmType")
    @JSONField(name = "AlarmType")

    private String AlarmType;
    /**
    * 告警时间
    */
    @TableField(value = "AlarmTime")
    @JSONField(name = "AlarmTime")
    @JsonFormat( pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date AlarmTime;
    /**
    * 用户id，多个，分割
    */
    @TableField(value = "UsersId")
    @JSONField(name = "UsersId")

    private String UsersId;
    /**
    * 用户姓名，多个，分割
    */
    @TableField(value = "UsersName")
    @JSONField(name = "UsersName")

    private String UsersName;
    /**
    * 告警内容
    */
    @TableField(value = "AlarmNote")
    @JSONField(name = "AlarmNote")

    private String AlarmNote;
    /**
    * 创建时间
    */
    @TableField(value = "CreateTime")
    @JSONField(name = "CreateTime")
    @JsonFormat( pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date CreateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
