package com.soft.gcc.xtbg.xcgl.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class GcjlTXcjhDto extends PageBaseEntity {
    private Integer deptId;

    private String userName;

    private String deptFullPath;
    /**
     * 行程开始日期
     */
    @JsonFormat( pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date planStartDate;

    /**
     * 行程结束日期
     */
    @JsonFormat( pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date planEndDate;

    private Integer approvingState;


    private Integer performState;
    /**
     * 确认状态（0未提交、1审批中、2已驳回、3确认通过）
     */
    private Integer notarizeState;

}
