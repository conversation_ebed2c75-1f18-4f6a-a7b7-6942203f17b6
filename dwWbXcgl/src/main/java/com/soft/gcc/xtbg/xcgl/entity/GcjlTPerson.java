package com.soft.gcc.xtbg.xcgl.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import lombok.Data;

/**
* <AUTHOR>
* 工程监理--人员表
* @TableName GCJL_T_Person
*/
@TableName(value ="GCJL_T_Person2")
@Data
public class GcjlTPerson implements Serializable {

    /**
    * 主键：
    */

    @TableId(value = "Id", type = IdType.AUTO)
    @JSONField(name = "Id")
    private Integer Id;

    /**
    * 老的用户表id
    */
    @TableField(value = "SrcUserId")
    @JSONField(name = "SrcUserId")

    private Integer SrcUserId;
    /**
    * 组织ID，备用
    */
    @TableField(value = "TopDeptId")
    @JSONField(name = "TopDeptId")

    private Integer TopDeptId;
    /**
    * 所属部门Id
    */
    @TableField(value = "DeptId")
    @JSONField(name = "DeptId")

    private Integer DeptId;
    /**
    * 用户全路径 宁波yy\yzyn\yzjlz\张三
    */
    @TableField(value = "UserFullPath")
    @JSONField(name = "UserFullPath")

    private String UserFullPath;
    /**
    * 状态(1启用 0 禁用)
    */
    @TableField(value = "State")
    @JSONField(name = "State")

    private Integer State;
    /**
    * 创建时间
    */
    @TableField(value = "CreateTime")
    @JSONField(name = "CreateTime")
    @JsonFormat( pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date CreateTime;
    /**
    * 费用上限
    */
    @TableField(value = "CostTopLimit")
    @JSONField(name = "CostTopLimit")

    private BigDecimal CostTopLimit;
    /**
    * 当天最后点距离公司里程
    */
    @TableField(value = "EndMileage")
    @JSONField(name = "EndMileage")

    private BigDecimal EndMileage;
    /**
    * 当天最后点距离公司费用
    */
    @TableField(value = "EndCost")
    @JSONField(name = "EndCost")

    private BigDecimal EndCost;
    /**
    * 是否是配网人员（0不是 1是）
    */
    @TableField(value = "IsNetPerson")
    @JSONField(name = "IsNetPerson")

    private Integer IsNetPerson;
    /**
    * 监理ID
    */
    @TableField(value = "JlId")
    @JSONField(name = "JlId")

    private Integer JlId;

    /**
     * 当天最后点区域监理ID
     */
    @TableField(value = "EndArrivedJlId")
    @JSONField(name = "EndArrivedJlId")
    private Integer EndArrivedJlId;
    /**
    * 当天最后点距离公司进入时间
    */
    @TableField(value = "EndArrivedTime")
    @JSONField(name = "EndArrivedTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date EndArrivedTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /*********导出excel用*********/
    /**
     * 姓名
     */
    @JSONField(name = "RealName")
    @TableField(exist = false)
    private String RealName;
    /**
     * 组织/部门名
     */
    @JSONField(name = "DeptName")
    @TableField(exist = false)
    private String DeptName;
    /**
     * 短号
     */
    @JSONField(name = "Sphone")
    @TableField(exist = false)
    private String Sphone;
    /**
     * 联系方式
     */
    @JSONField(name = "Telephone")
    @TableField(exist = false)
    private String Telephone;
    /**
     * 状态
     */
    @JSONField(name = "StateStr")
    @TableField(exist = false)
    private String StateStr;
    /***********************/
}
