package com.soft.gcc.xtbg.xcgl.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import lombok.Data;

/**
* <AUTHOR>
*
* @TableName GCJL_T_JlConfig
*/
@TableName(value ="GCJL_T_JlConfig")
@Data
public class GcjlTJlconfig implements Serializable {

    /**
    * 主键：主键id
    */

    @TableId(value = "Id", type = IdType.AUTO)
    @JSONField(name = "Id")
    private Integer Id;

    /**
    * 监理站名称
    */
    @TableField(value = "JlName")
    @JSONField(name = "JlName")

    private String JlName;
    /**
    * 纬度
    */
    @TableField(value = "Latitude")
    @JSONField(name = "Latitude")

    private BigDecimal Latitude;
    /**
    * 经度
    */
    @TableField(value = "Longitude")
    @JSONField(name = "Longitude")

    private BigDecimal Longitude;
    /**
    * 省份
    */
    @TableField(value = "Provinces")
    @JSONField(name = "Provinces")

    private Integer Provinces;
    /**
    * 市
    */
    @TableField(value = "City")
    @JSONField(name = "City")

    private Integer City;
    /**
    * 区
    */
    @TableField(value = "Area")
    @JSONField(name = "Area")

    private Integer Area;
    /**
    * 创建时间
    */
    @TableField(value = "CreateTime")
    @JSONField(name = "CreateTime")
    @JsonFormat( pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date CreateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
