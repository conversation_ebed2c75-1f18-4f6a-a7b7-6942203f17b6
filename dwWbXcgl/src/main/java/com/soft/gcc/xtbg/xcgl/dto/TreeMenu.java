package com.soft.gcc.xtbg.xcgl.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Data
@Accessors(chain = true)
public class TreeMenu {
        String label;
        Integer id;
        Integer pid;
        List<TreeMenu> children;
     public Set<Integer> getChildrenIdsSet(TreeMenu  treeMenu){
             Set<Integer> idsSet=new HashSet<>();
             if(treeMenu!=null && treeMenu.getId()!=null){
                     idsSet.add(treeMenu.getId());
                     idsSet=getChildrenId(idsSet,treeMenu);
             }
             return idsSet;
     }

     private Set<Integer> getChildrenId( Set<Integer> idsSet,TreeMenu treeMenu){
             List<TreeMenu> children = treeMenu.getChildren();
             if(children!=null &&children.size()>0){
                     for(TreeMenu ct:children){
                             Integer id = ct.getId();
                             idsSet.add(id);
                             getChildrenId(idsSet,ct);
                     }
             }
             return idsSet;
     }

}
