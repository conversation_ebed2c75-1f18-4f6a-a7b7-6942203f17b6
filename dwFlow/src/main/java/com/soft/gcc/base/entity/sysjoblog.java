package com.soft.gcc.base.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * sysjoblog
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sysjoblog")
@ApiModel(value="sysjoblog对象", description="sysjoblog")
public class sysjoblog extends Model<sysjoblog> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "日志ID")
    @TableId(value = "LogId", type = IdType.AUTO)
    @JSONField(name="LogId")
    private Long LogId;

    @ApiModelProperty(value = "任务名称")
    @JSONField(name="JobName")
    private Integer JobName;

    @ApiModelProperty(value = "任务组名")
    @JSONField(name="JobGroup")
    private String JobGroup;

    @ApiModelProperty(value = "调用目标字符串")
    @JSONField(name="InvokeTarget")
    private String InvokeTarget;

    @ApiModelProperty(value = "开始时间")
    @JSONField(name="StartTime")
    private LocalDateTime StartTime;

    @ApiModelProperty(value = "结束时间")
    @JSONField(name="StopTime")
    private LocalDateTime StopTime;

    @ApiModelProperty(value = "日志信息")
    @JSONField(name="JobMessage")
    private String JobMessage;

    @ApiModelProperty(value = "执行状态0=正常,1=失败")
    @JSONField(name="Status")
    private String Status;

    @ApiModelProperty(value = "异常信息")
    @JSONField(name="ExceptionInfo")
    private String ExceptionInfo;


    @Override
    protected Serializable pkVal() {
        return this.LogId;
    }

}
