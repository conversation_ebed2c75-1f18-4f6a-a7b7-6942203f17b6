.icon-blank{
	background:url('icons/blank.gif') no-repeat center center;
}
.icon-add{
	background:url('icons/edit_add.png') no-repeat center center;
}
.icon-edit{
	background:url('icons/pencil.png') no-repeat center center;
}
.icon-clear{
	background:url('icons/clear.png') no-repeat center center;
}
.icon-remove{
	background:url('icons/edit_remove.png') no-repeat center center;
}
.icon-save{
	background:url('icons/filesave.png') no-repeat center center;
}
.icon-cut{
	background:url('icons/cut.png') no-repeat center center;
}
.icon-ok{
	background:url('icons/ok.png') no-repeat center center;
}
.icon-no{
	background:url('icons/no.png') no-repeat center center;
}
.icon-cancel{
	background:url('icons/cancel.png') no-repeat center center;
}
.icon-reload{
	background:url('icons/reload.png') no-repeat center center;
}
.icon-search{
	background:url('icons/search.png') no-repeat center center;
}
.icon-print{
	background:url('icons/print.png') no-repeat center center;
}
.icon-help{
	background:url('icons/help.png') no-repeat center center;
}
.icon-undo{
	background:url('icons/undo.png') no-repeat center center;
}
.icon-redo{
	background:url('icons/redo.png') no-repeat center center;
}
.icon-back{
	background:url('icons/back.png') no-repeat center center;
}
.icon-sum{
	background:url('icons/sum.png') no-repeat center center;
}
.icon-tip{
	background:url('icons/tip.png') no-repeat center center;
}
.icon-filter{
	background:url('icons/filter.png') no-repeat center center;
}
.icon-man{
	background:url('icons/man.png') no-repeat center center;
}
.icon-lock{
	background:url('icons/lock.png') no-repeat center center;
}
.icon-more{
	background:url('icons/more.png') no-repeat center center;
}


.icon-mini-add{
	background:url('icons/mini_add.png') no-repeat center center;
}
.icon-mini-edit{
	background:url('icons/mini_edit.png') no-repeat center center;
}
.icon-mini-refresh{
	background:url('icons/mini_refresh.png') no-repeat center center;
}

.icon-large-picture{
	background:url('icons/large_picture.png') no-repeat center center;
}
.icon-large-clipart{
	background:url('icons/large_clipart.png') no-repeat center center;
}
.icon-large-shapes{
	background:url('icons/large_shapes.png') no-repeat center center;
}
.icon-large-smartart{
	background:url('icons/large_smartart.png') no-repeat center center;
}
.icon-large-chart{
	background:url('icons/large_chart.png') no-repeat center center;
}


/*2022年1月19日 10点08分 为了升级easyui发现很多直接在插件基础上修改的样式*/
/*			 真真是有那个大病，草*/

.icon-blank{
	background:url('icons/blank.gif') no-repeat center center;
}
.icon-add{
	background:url('icons/edit_add.png') no-repeat center center;
}
.icon-edit{
	background:url('icons/pencil.png') no-repeat center center;
}
.icon-clear{
	background:url('icons/clear.png') no-repeat center center;
}
.icon-remove{
	background:url('icons/edit_remove.png') no-repeat center center;
}
.icon-save{
	background:url('icons/filesave.png') no-repeat center center;
}
.icon-cut{
	background:url('icons/cut.png') no-repeat center center;
}
.icon-ok{
	background:url('icons/ok.png') no-repeat center center;
}
.icon-send{
	background:url('icons/send.png') no-repeat center center;
}
.icon-disabled{
	background:url('icons/disabled.png') no-repeat center center;
}
.icon-no{
	background:url('icons/no.png') no-repeat center center;
}
.icon-cancel{
	background:url('icons/cancel.png') no-repeat center center;
}
.icon-reload{
	background:url('icons/reload.png') no-repeat center center;
}
.icon-search{
	background:url('icons/search.png') no-repeat center center;
}
.icon-print{
	background:url('icons/print.png') no-repeat center center;
}
.icon-help{
	background:url('icons/help.png') no-repeat center center;
}
.icon-undo{
	background:url('icons/undo.png') no-repeat center center;
}
.icon-redo{
	background:url('icons/redo.png') no-repeat center center;
}
.icon-back{
	background:url('icons/back.png') no-repeat center center;
}
.icon-sum{
	background:url('icons/sum.png') no-repeat center center;
}
.icon-tip{
	background:url('icons/tip.png') no-repeat center center;
}

.icon-mini-add{
	background:url('icons/mini_add.png') no-repeat center center;
}
.icon-mini-edit{
	background:url('icons/mini_edit.png') no-repeat center center;
}
.icon-mini-refresh{
	background:url('icons/mini_refresh.png') no-repeat center center;
}
.icon-lead{
	background:url('icons/1.jpg') no-repeat center center;
}
.icon-export{
	background:url('icons/2.jpg') no-repeat center center;
}
.icon-export{
	background:url('icons/2.jpg') no-repeat center center;
}
.arrow-one-left{
	background:url('icons/arrow-one-left.png') no-repeat center center;
}
.arrow-one-right{
	background:url('icons/arrow-one-right.png') no-repeat center center;
}
.arrow-all-left{
	background:url('icons/arrow-all-left.png') no-repeat center center;
}
.arrow-all-right{
	background:url('icons/arrow-all-right.png') no-repeat center center;
}
.icon-arrow-top{
	background:url('icons/arrow_top.png') no-repeat center center;
}

.icon-arrow-bottom{
	background:url('icons/arrow_bottom.png') no-repeat center center;
}
.icon-cancel-edit{
	background:url('icons/cancel_edit.png') no-repeat center center;
}
.icon-filesave{
	background:url('icons/filesave.png') no-repeat center center;
}
.icon-pencil{
	background:url('icons/pencil.png') no-repeat center center;

}
.icon-open{
	background:url('icons/open.png') no-repeat center center;
}
.icon-look{
	background:url('icons/look.png') no-repeat center center;
}
.icon-btnProduction{
	background:url('icons/btnProduction.png') no-repeat center center;
}
.icon-copy{
	background:url('icons/copy.png') no-repeat center center;
}
.icon-arrow_bottom{
	background:url('icons/arrow_bottom.png') no-repeat center center;
}
.icon-play{
	background:url('icons/play.png') no-repeat center center;
}
.icon-location{
	background:url('../../img/location.png') no-repeat center center;
}