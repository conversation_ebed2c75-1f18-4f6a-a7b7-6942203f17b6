.spinner-arrow {
  display: inline-block;
  overflow: hidden;
  vertical-align: top;
  margin: 0;
  padding: 0;
  opacity: 1.0;
  filter: alpha(opacity=100);
  width: 18px;
}
.spinner-arrow.spinner-button-top,
.spinner-arrow.spinner-button-bottom,
.spinner-arrow.spinner-button-left,
.spinner-arrow.spinner-button-right {
  background-color: #F2F2F2;
}
.spinner-arrow-up,
.spinner-arrow-down {
  opacity: 0.6;
  filter: alpha(opacity=60);
  display: block;
  font-size: 1px;
  width: 18px;
  height: 10px;
  width: 100%;
  height: 50%;
  color: #444;
  outline-style: none;
  background-color: #F2F2F2;
}
.spinner-button-updown {
  opacity: 1.0;
}
.spinner-button-updown .spinner-button-top,
.spinner-button-updown .spinner-button-bottom {
  position: relative;
  display: block;
  width: 100%;
  height: 50%;
}
.spinner-button-updown .spinner-arrow-up,
.spinner-button-updown .spinner-arrow-down {
  opacity: 1.0;
  filter: alpha(opacity=100);
  cursor: pointer;
  width: 16px;
  height: 16px;
  top: 50%;
  left: 50%;
  margin-top: -8px;
  margin-left: -8px;
  position: absolute;
}
.spinner-button-updown .spinner-button-top,
.spinner-button-updown .spinner-button-bottom {
  cursor: pointer;
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.spinner-button-updown .spinner-button-top:hover,
.spinner-button-updown .spinner-button-bottom:hover {
  opacity: 1.0;
  filter: alpha(opacity=100);
}
.spinner-button-updown .spinner-arrow-up,
.spinner-button-updown .spinner-arrow-down,
.spinner-button-updown .spinner-arrow-up:hover,
.spinner-button-updown .spinner-arrow-down:hover {
  background-color: transparent;
}
.spinner-arrow-hover {
  background-color: #e6e6e6;
  opacity: 1.0;
  filter: alpha(opacity=100);
}
.spinner-button-top:hover,
.spinner-button-bottom:hover,
.spinner-button-left:hover,
.spinner-button-right:hover,
.spinner-arrow-up:hover,
.spinner-arrow-down:hover {
  opacity: 1.0;
  filter: alpha(opacity=100);
  background-color: #e6e6e6;
}
.textbox-disabled .spinner-button-top:hover,
.textbox-disabled .spinner-button-bottom:hover,
.textbox-disabled .spinner-button-left:hover,
.textbox-disabled .spinner-button-right:hover,
.textbox-icon-disabled .spinner-arrow-up:hover,
.textbox-icon-disabled .spinner-arrow-down:hover {
  opacity: 0.6;
  filter: alpha(opacity=60);
  background-color: #F2F2F2;
  cursor: default;
}
.spinner .textbox-icon-disabled {
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.spinner-arrow-up {
  background: url('images/spinner_arrows.png') no-repeat 1px center;
  background-color: #F2F2F2;
}
.spinner-arrow-down {
  background: url('images/spinner_arrows.png') no-repeat -15px center;
  background-color: #F2F2F2;
}
.spinner-button-up {
  background: url('images/spinner_arrows.png') no-repeat -32px center;
}
.spinner-button-down {
  background: url('images/spinner_arrows.png') no-repeat -48px center;
}
