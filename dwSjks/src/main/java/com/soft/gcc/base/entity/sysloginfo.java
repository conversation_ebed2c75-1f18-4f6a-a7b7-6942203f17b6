package com.soft.gcc.base.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * sysloginfo
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sysloginfo")
@ApiModel(value="sysloginfo对象", description="sysloginfo")
public class sysloginfo extends Model<sysloginfo> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "记录ID")
    @TableId(value = "LogId", type = IdType.AUTO)
    @JSONField(name="LogId")
    private Long LogId;

    @ApiModelProperty(value = "用户ID")
    @JSONField(name="UserName")
    private String UserName;

    @ApiModelProperty(value = "IP地址")
    @JSONField(name="IpAddr")
    private String IpAddr;

    @ApiModelProperty(value = "登录地点")
    @JSONField(name="LoginLocation")
    private String LoginLocation;

    @ApiModelProperty(value = "浏览器")
    @JSONField(name="Browser")
    private String Browser;

    @ApiModelProperty(value = "操作系统")
    @JSONField(name="Os")
    private String Os;

    @ApiModelProperty(value = "消息")
    @JSONField(name="Msg")
    private String Msg;

    @ApiModelProperty(value = "状态")
    @JSONField(name="Status")
    private String Status;

    @ApiModelProperty(value = "访问时间")
    @JSONField(name="loginTime")
    private LocalDateTime loginTime;


    @Override
    protected Serializable pkVal() {
        return this.LogId;
    }

}
