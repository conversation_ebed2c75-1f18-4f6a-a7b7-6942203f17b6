package com.soft.gcc.base.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 模块--企业定制
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("NFT_ModuleQydz")
@ApiModel(value="NFT_ModuleQydz对象", description="模块--企业定制")
public class NFT_ModuleQydz extends Model<NFT_ModuleQydz> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "企业定制ID")
    @JSONField(name="QYDZ_ID")
    private Integer QYDZ_ID;

    @ApiModelProperty(value = "适用企业ID")
    @JSONField(name="QYDZ_CID")
    private Integer QYDZ_CID;

    @ApiModelProperty(value = "企业定制模块列表")
    @JSONField(name="QYDZ_MLIST")
    private String QYDZ_MLIST;


    @Override
    protected Serializable pkVal() {
        return this.QYDZ_ID;
    }

}
