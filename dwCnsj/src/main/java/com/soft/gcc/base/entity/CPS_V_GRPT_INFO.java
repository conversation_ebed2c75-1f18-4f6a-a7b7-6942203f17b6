package com.soft.gcc.base.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("CPS_V_GRPT_INFO")
@ApiModel(value="CPS_V_GRPT_INFO对象", description="")
public class CPS_V_GRPT_INFO extends Model<CPS_V_GRPT_INFO> {

    private static final long serialVersionUID = 1L;

    @JSONField(name="GRPTT_NAME")
    private String GRPTT_NAME;

    @JSONField(name="GRPT_SFILE")
    private String GRPT_SFILE;

    @JSONField(name="GRPTT_ID")
    private Integer GRPTT_ID;

    @JSONField(name="GRPTT_CODE")
    private String GRPTT_CODE;

    @JSONField(name="GRPT_DEF")
    private Integer GRPT_DEF;

    @JSONField(name="GRPTT_REMARK")
    private String GRPTT_REMARK;

    @JSONField(name="GRPT_SMARK")
    private String GRPT_SMARK;

    @JSONField(name="GRPT_DEFN")
    private String GRPT_DEFN;

    @JSONField(name="GRPT_ID")
    private Integer GRPT_ID;


    @Override
    protected Serializable pkVal() {
        return null;
    }

}
