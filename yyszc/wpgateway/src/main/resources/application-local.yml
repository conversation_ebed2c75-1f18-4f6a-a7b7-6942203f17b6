management:
  endpoints:
    web:
      exposure:
        include: '*'
spring:
  application:
    name: wpgatway
  cloud:
    loadbalancer:
      cache:
        enabled: true
        caffeine:
          spec: initialCapacity=500,expireAfterWrite=5s
    sentinel:
      transport:
        dashboard: 127.0.0.1:8080
        port: 8179
      eager: true
      enabled: true
      scg:
        fallback:
          mode: response
          response-status: 455
          response-body: The system is busy, please try again later!
    gateway:
      discovery:
        locator:
          enabled: true
      routes:
        - id: router_wpframe
          uri: lb://wpframe
          predicates:
            - Path=/wpframe/**
          filters:
            - StripPrefix=0
server:
  port: 57000
# 日志配置
logging:
  config:
    classpath: log4j2.xml
