package com.soft.framework.filter;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;


@Component
public class TimeFilter implements GlobalFilter, Ordered {

    private static final Log logger = LogFactory.getLog(TimeFilter.class);

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        //定个开始开始时间
        exchange.getAttributes().put("beginTime",System.currentTimeMillis());
        return chain.filter(exchange).then(
                Mono.fromRunnable(()->{
                    Long beginTime = exchange.getAttribute("beginTime");
                    if (beginTime!=null){
                        logger.info(">>>>>>请求"+exchange.getRequest().getURI().getRawPath()+"耗时: "+(System.currentTimeMillis() - beginTime) + "ms");
                    }
                })
        );
    }

    @Override
    public int getOrder() {
        //定义优先级
        return 2;
    }

}
