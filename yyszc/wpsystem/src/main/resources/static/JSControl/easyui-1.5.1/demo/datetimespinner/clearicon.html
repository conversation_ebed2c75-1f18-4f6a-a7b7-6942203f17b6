<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>DateTimeSpinner with Clear Icon - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../easyui-1.5.1/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>DateTimeSpinner with Clear Icon</h2>
	<p>A clear icon can be attached to the datetimespinner. Click it to clear the entered value.</p>
	<div style="margin:20px 0;"></div>
	<div class="easyui-panel" style="width:100%;max-width:400px;padding:30px 60px;">
		<div style="margin-bottom:20px">
			<input class="easyui-datetimespinner" value="6/24/2015 17:23:40" style="width:100%;" data-options="
					showSeconds: true,
					prompt: 'Input date time here!',
					icons:[{
						iconCls:'icon-clear',
						handler: function(e){
							$(e.data.target).datetimespinner('clear');
						}
					}],
					label: 'Start Time:',
					labelPosition: 'top'
					">
		</div>
	</div>
</body>
</html>