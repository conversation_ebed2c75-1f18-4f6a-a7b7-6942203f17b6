package cgenerator;

import com.baomidou.mybatisplus.generator.config.INameConvert;
import com.baomidou.mybatisplus.generator.config.po.TableField;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;

/**
 * Created with IntelliJ IDEA.
 * Date: 2020/5/20
 * Time: 10:45
 *
 * <AUTHOR>
 * Description:  自定义的表与实体转换策略
 */
public class CNameConvert implements INameConvert {
    public String entityNameConvert(TableInfo tableInfo) {
        return tableInfo.getName();
    }

    public String propertyNameConvert(TableField field) {
        return field.getName();
    }
}