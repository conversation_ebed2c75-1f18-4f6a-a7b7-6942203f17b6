package cgenerator;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;

public class BaseDO {

    public static void main(String[] args) {
        String rootpath = System.getProperty("user.dir");

        try
        {
            // 代码生成器
            AutoGenerator mpg = new AutoGenerator();

            // 全局配置
            GlobalConfig gc = new GlobalConfig();

            //生成文件的输出目录
            gc.setOutputDir(rootpath+"/src/main/java/");
            gc.setAuthor("qidefang");
            gc.setOpen(false);
            gc.setFileOverride(false);
            gc.setActiveRecord(true);
            gc.setSwagger2(true);
            mpg.setGlobalConfig(gc);

            DataSourceConfig dsc = new DataSourceConfig();
            dsc.setUrl("********************************************************;");
            dsc.setDriverName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
            dsc.setUsername("sa");
            dsc.setDbType(DbType.SQL_SERVER);
            dsc.setPassword("sa");
            mpg.setDataSource(dsc);

            // 包配置
            PackageConfig pc = new PackageConfig();
            pc.setParent("com.yyszc.wpbase");
            pc.setEntity("entity");
            pc.setService("service");
            pc.setServiceImpl("service.impl");
            pc.setController("controller");
            pc.setMapper("mapper");
            pc.setXml("xml");
            mpg.setPackageInfo(pc);

            // 配置模板
            TemplateConfig templateConfig = new TemplateConfig();
            templateConfig.setMapper(null);
            templateConfig.setService(null);
            templateConfig.setController(null);
            templateConfig.setServiceImpl(null);
            templateConfig.setXml(null);
            mpg.setTemplate(templateConfig);

            String [] tables=new String[]{
                "Module","NFT_SQL_IDSTR",
                "NFT_ModuleAMLink","NFT_ModuleLCLink","NFT_WorkFlow","NFT_NWIPD","NFT_WhiteList","NFT_ModuleGroup",
                "FunctionItem","GroupItem", "Lc_Condition", "Lc_currentState", "Lc_tojd", "Lc_workFlow", "Lcdefine", "Lcjd", "Permission", "Person",
                "Role", "RolePermission", "RolePerson","DictionaryDefine","DictionaryValue",
                "vComp","vFunctionItem","NFV_ModuleAMLink", "NFV_ModuleLCLink","NFV_WorkFlow",
                "vGroupItem", "vPerson","EmailManage","EmailDetail","v$EmailDetail","vDictionaryValue"
            };

            // 策略配置X
            StrategyConfig strategy = new StrategyConfig();
            strategy.setNaming(NamingStrategy.no_change);
            strategy.setColumnNaming(NamingStrategy.no_change);
            strategy.setCapitalMode(false);
            strategy.setEntityLombokModel(true);
            strategy.setRestControllerStyle(false);
            strategy.setEntityColumnConstant(false);
            strategy.setSkipView(false);
            strategy.setInclude(tables);
            strategy.setEntityLombokModel(true);
            strategy.setEntityTableFieldAnnotationEnable(true);
            strategy.setControllerMappingHyphenStyle(false);
            strategy.setNameConvert(new CNameConvert());
            mpg.setStrategy(strategy);

            FreemarkerTemplateEngine freemarkerTemplateEngine=new FreemarkerTemplateEngine();
            freemarkerTemplateEngine.templateFilePath("/templates/");
            //设置模板引擎
            mpg.setTemplateEngine(freemarkerTemplateEngine);
            mpg.execute();
        }catch(Exception Ex)
        {
            String exmsg=Ex.getMessage();
        }
    }
}
