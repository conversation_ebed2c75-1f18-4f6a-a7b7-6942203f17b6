package com.yyszc.wpbase.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("NFT_WorkFlow")
@ApiModel(value="NFT_WorkFlow对象", description="")
public class NFT_WorkFlow extends Model<NFT_WorkFlow> {

    private static final long serialVersionUID = 1L;

    @JSONField(name="ywName")
    private String ywName;

    @JSONField(name="lc_defineID")
    private Integer lc_defineID;

    @JSONField(name="ywID")
    private Integer ywID;


    @Override
    protected Serializable pkVal() {
        return this.lc_defineID;
    }

}
