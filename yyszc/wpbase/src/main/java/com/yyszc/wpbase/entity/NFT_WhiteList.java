package com.yyszc.wpbase.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 导航信息表--白名单
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("NFT_WhiteList")
@ApiModel(value="NFT_WhiteList对象", description="导航信息表--白名单")
public class NFT_WhiteList extends Model<NFT_WhiteList> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "标识")
    @TableId(value = "Id", type = IdType.AUTO)
    @JSONField(name="Id")
    private Integer Id;

    @ApiModelProperty(value = "IP地址")
    @JSONField(name="IpAddr")
    private String IpAddr;

    @ApiModelProperty(value = "起点地址")
    @JSONField(name="FuncName")
    private String FuncName;


    @Override
    protected Serializable pkVal() {
        return this.Id;
    }

}
