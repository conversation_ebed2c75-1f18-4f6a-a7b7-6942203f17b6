package com.yyszc.wpbase.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 导航信息表--模块流程关联
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("NFT_ModuleLCLink")
@ApiModel(value="NFT_ModuleLCLink对象", description="导航信息表--模块流程关联")
public class NFT_ModuleLCLink extends Model<NFT_ModuleLCLink> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "记录编号")
    @TableId(value = "LK_ID", type = IdType.AUTO)
    @JSONField(name="LK_ID")
    private Integer LK_ID;

    @ApiModelProperty(value = "模块ID")
    @JSONField(name="LK_MID")
    private Integer LK_MID;

    @ApiModelProperty(value = "流程ID")
    @JSONField(name="LK_LCID")
    private Integer LK_LCID;

    @JSONField(name="LK_TNODE")
    private String LK_TNODE;

    @JSONField(name="LK_TMENU")
    private String LK_TMENU;


    @Override
    protected Serializable pkVal() {
        return this.LK_ID;
    }

}
