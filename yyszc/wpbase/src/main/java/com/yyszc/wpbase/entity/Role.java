package com.yyszc.wpbase.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("Role")
@ApiModel(value="Role对象", description="")
public class Role extends Model<Role> {

    private static final long serialVersionUID = 1L;

    @JSONField(name="RoleName")
    private String RoleName;

    @JSONField(name="RoleK<PERSON>")
    private String RoleKind;

    @JSONField(name="AdminGroupIds")
    private String AdminGroupIds;

    @JSONField(name="Id")
    private Integer Id;

    @JSONField(name="IsHide")
    private Boolean IsHide;


    @Override
    protected Serializable pkVal() {
        return null;
    }

}
