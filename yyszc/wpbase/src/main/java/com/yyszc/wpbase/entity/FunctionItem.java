package com.yyszc.wpbase.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("FunctionItem")
@ApiModel(value="FunctionItem对象", description="")
public class FunctionItem extends Model<FunctionItem> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "模块ID(对应module表)")
    @JSONField(name="module_ID")
    private Integer module_ID;

    @ApiModelProperty(value = "APP所属模块")
    @JSONField(name="app_module")
    private Integer app_module;

    @ApiModelProperty(value = "APP是否企业定制1是")
    @JSONField(name="app_type")
    private Integer app_type;

    @ApiModelProperty(value = "APP自定义图标")
    @JSONField(name="app_icon")
    private String app_icon;

    @JSONField(name="Id")
    private String Id;

    @JSONField(name="ParentId")
    private String ParentId;

    @JSONField(name="app_url")
    private String app_url;

    @JSONField(name="Parameter")
    private Integer Parameter;

    @JSONField(name="Title")
    private String Title;

    @JSONField(name="OrderNumber")
    private Integer OrderNumber;

    @JSONField(name="DisplayName")
    private String DisplayName;

    @JSONField(name="IsPublic")
    private Integer IsPublic;

    @JSONField(name="Url")
    private String Url;


    @Override
    protected Serializable pkVal() {
        return null;
    }

}
