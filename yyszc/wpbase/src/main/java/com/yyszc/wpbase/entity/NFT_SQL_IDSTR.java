package com.yyszc.wpbase.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 导航信息表--SQL注入检测串
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("NFT_SQL_IDSTR")
@ApiModel(value="NFT_SQL_IDSTR对象", description="导航信息表--SQL注入检测串")
public class NFT_SQL_IDSTR extends Model<NFT_SQL_IDSTR> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "标识")
    @TableId(value = "ID", type = IdType.AUTO)
    @JSONField(name="ID")
    private Integer ID;

    @ApiModelProperty(value = "串检测")
    @JSONField(name="SIDS")
    private String SIDS;

    @ApiModelProperty(value = "串检测类型(0：首位加空格，1:不加空格）")
    @JSONField(name="SIDT")
    private Integer SIDT;


    @Override
    protected Serializable pkVal() {
        return this.ID;
    }

}
