package com.yyszc.wpbase.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("Permission")
@ApiModel(value="Permission对象", description="")
public class Permission extends Model<Permission> {

    private static final long serialVersionUID = 1L;

    @JSONField(name="Operation")
    private String Operation;

    @JSONField(name="PermissionNo")
    private String PermissionNo;

    @JSONField(name="Subsystem")
    private String Subsystem;

    @JSONField(name="PermissionKind")
    private String PermissionKind;

    @JSONField(name="OperationModule")
    private String OperationModule;


    @Override
    protected Serializable pkVal() {
        return this.PermissionNo;
    }

}
