package com.yyszc.wpbase.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("Person")
@ApiModel(value="Person对象", description="")
public class Person extends Model<Person> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "物资系统对应用户ID")
    @JSONField(name="WZ_UserID")
    private Integer WZ_UserID;

    @ApiModelProperty(value = "用于判断是集团企业还是施工单位(管理员1，其他是2)")
    @JSONField(name="type")
    private Integer type;

    @ApiModelProperty(value = "短号")
    @JSONField(name="Sphone")
    private String Sphone;

    @ApiModelProperty(value = "专家次数")
    @JSONField(name="ZJ_CS")
    private Integer ZJ_CS;

    @ApiModelProperty(value = "是否有签名照")
    @JSONField(name="PhName")
    private Integer PhName;

    @ApiModelProperty(value = "身份证号码")
    @JSONField(name="CertificateID")
    private String CertificateID;

    @ApiModelProperty(value = "办公室电话")
    @JSONField(name="OfficePhone")
    private String OfficePhone;

    @JSONField(name="OA")
    private String OA;

    @JSONField(name="RealName")
    private String RealName;

    @JSONField(name="RoleId")
    private Integer RoleId;

    @JSONField(name="BFLoginName")
    private String BFLoginName;

    @JSONField(name="oldID")
    private Integer oldID;

    @JSONField(name="state")
    private Integer state;

    @TableId(value = "Id", type = IdType.AUTO)
    @JSONField(name="Id")
    private Integer Id;

    @JSONField(name="Password")
    private String Password;

    @JSONField(name="P_XH")
    private Integer P_XH;

    @JSONField(name="LoginName2")
    private String LoginName2;

    @JSONField(name="Telephone")
    private String Telephone;

    @JSONField(name="GroupID")
    private Integer GroupID;

    @JSONField(name="LoginName")
    private String LoginName;

    @JSONField(name="MsgType")
    private String MsgType;


    @Override
    protected Serializable pkVal() {
        return this.Id;
    }

}
