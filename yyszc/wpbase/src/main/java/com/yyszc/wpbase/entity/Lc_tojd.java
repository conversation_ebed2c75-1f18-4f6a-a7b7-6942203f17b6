package com.yyszc.wpbase.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("Lc_tojd")
@ApiModel(value="Lc_tojd对象", description="")
public class Lc_tojd extends Model<Lc_tojd> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "业务id")
    @JSONField(name="ywID")
    private Integer ywID;

    @ApiModelProperty(value = "流程id")
    @JSONField(name="LcDefineID")
    private Integer LcDefineID;

    @ApiModelProperty(value = "班组id")
    @JSONField(name="groupID")
    private Integer groupID;

    @ApiModelProperty(value = "人id")
    @JSONField(name="personId")
    private Integer personId;

    @ApiModelProperty(value = "是否已经完成")
    @JSONField(name="isUse")
    private Integer isUse;

    @ApiModelProperty(value = "流向阶段id")
    @JSONField(name="lcjdId")
    private Integer lcjdId;

    @ApiModelProperty(value = "流向班组或人")
    @JSONField(name="toGroupOrPer")
    private Integer toGroupOrPer;

    @JSONField(name="ID")
    private Integer ID;


    @Override
    protected Serializable pkVal() {
        return null;
    }

}
