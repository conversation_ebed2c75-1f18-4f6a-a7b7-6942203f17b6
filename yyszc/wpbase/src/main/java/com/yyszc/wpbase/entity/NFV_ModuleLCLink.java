package com.yyszc.wpbase.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 导航信息表--模块流程关联
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("NFV_ModuleLCLink")
@ApiModel(value="NFV_ModuleLCLink对象", description="导航信息表--模块流程关联")
public class NFV_ModuleLCLink extends Model<NFV_ModuleLCLink> {

    private static final long serialVersionUID = 1L;

    @JSONField(name="LK_TMENU")
    private String LK_TMENU;

    @JSONField(name="LK_ID")
    private Integer LK_ID;

    @JSONField(name="app_url")
    private String app_url;

    @JSONField(name="lcName")
    private String lcName;

    @JSONField(name="module_name")
    private String module_name;

    @JSONField(name="is_show")
    private Integer is_show;

    @JSONField(name="LK_MID")
    private Integer LK_MID;

    @JSONField(name="LK_TNODE")
    private String LK_TNODE;

    @JSONField(name="define_f")
    private String define_f;

    @JSONField(name="LcID")
    private Integer LcID;

    @JSONField(name="LK_LCID")
    private Integer LK_LCID;

    @JSONField(name="module_num")
    private String module_num;

    @JSONField(name="url")
    private String url;

    @JSONField(name="define_s")
    private String define_s;


    @Override
    protected Serializable pkVal() {
        return null;
    }

}
