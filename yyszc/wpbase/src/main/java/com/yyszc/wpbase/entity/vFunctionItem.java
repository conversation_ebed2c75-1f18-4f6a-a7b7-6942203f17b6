package com.yyszc.wpbase.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * vFunctionItem
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("vFunctionItem")
@ApiModel(value="vFunctionItem对象", description="vFunctionItem")
public class vFunctionItem extends Model<vFunctionItem> {

    private static final long serialVersionUID = 1L;

    @JSONField(name="Id")
    private String Id;

    @JSONField(name="Parameter")
    private Integer Parameter;

    @JSONField(name="ParentId")
    private String ParentId;

    @JSONField(name="app_url")
    private String app_url;

    @JSONField(name="app_icon")
    private String app_icon;

    @JSONField(name="OrderNumber")
    private Integer OrderNumber;

    @JSONField(name="Title")
    private String Title;

    @JSONField(name="app_module")
    private Integer app_module;

    @JSONField(name="ParentName")
    private String ParentName;

    @JSONField(name="DisplayName")
    private String DisplayName;

    @JSONField(name="IsPublic")
    private Integer IsPublic;

    @JSONField(name="Url")
    private String Url;

    @JSONField(name="module_ID")
    private Integer module_ID;

    @JSONField(name="app_type")
    private Integer app_type;


    @Override
    protected Serializable pkVal() {
        return null;
    }

}
