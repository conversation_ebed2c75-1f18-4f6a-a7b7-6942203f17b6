package com.yyszc.wpbase.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * vPerson
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("vPerson")
@ApiModel(value="vPerson对象", description="vPerson")
public class vPerson extends Model<vPerson> {

    private static final long serialVersionUID = 1L;

    @JSONField(name="type")
    private Integer type;

    @JSONField(name="ParentId")
    private Integer ParentId;

    @JSONField(name="Telephone")
    private String Telephone;

    @JSONField(name="TopGroupName")
    private String TopGroupName;

    @JSONField(name="GroupName")
    private String GroupName;

    @JSONField(name="LoginName")
    private String LoginName;

    @JSONField(name="MsgType")
    private String MsgType;

    @JSONField(name="Password")
    private String Password;

    @JSONField(name="GroupDesc")
    private String GroupDesc;

    @JSONField(name="ParentName")
    private String ParentName;

    @JSONField(name="P_XH")
    private Integer P_XH;

    @JSONField(name="LoginName2")
    private String LoginName2;

    @JSONField(name="GroupId")
    private Integer GroupId;

    @JSONField(name="RoleId")
    private Integer RoleId;

    @JSONField(name="OA")
    private String OA;

    @JSONField(name="TopGroupId")
    private Integer TopGroupId;

    @JSONField(name="GroupType")
    private Integer GroupType;

    @JSONField(name="RealName")
    private String RealName;

    @JSONField(name="Id")
    private Integer Id;


    @Override
    protected Serializable pkVal() {
        return null;
    }

}
