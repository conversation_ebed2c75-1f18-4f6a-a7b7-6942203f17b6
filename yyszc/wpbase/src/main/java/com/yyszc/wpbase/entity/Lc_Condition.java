package com.yyszc.wpbase.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("Lc_Condition")
@ApiModel(value="Lc_Condition对象", description="")
public class Lc_Condition extends Model<Lc_Condition> {

    private static final long serialVersionUID = 1L;

    @JSONField(name="Condition")
    private String Condition;

    @JSONField(name="Z_Condition")
    private String Z_Condition;

    @JSONField(name="NextJDID")
    private Integer NextJDID;

    @JSONField(name="ISBX")
    private Integer ISBX;

    @JSONField(name="L_Condition")
    private String L_Condition;

    @JSONField(name="FromJScdt")
    private String FromJScdt;

    @JSONField(name="LC_DefineID")
    private Integer LC_DefineID;

    @JSONField(name="PID")
    private Integer PID;

    @JSONField(name="YWB")
    private String YWB;

    @JSONField(name="JDMC")
    private String JDMC;

    @JSONField(name="NextJDMC")
    private String NextJDMC;

    @JSONField(name="JDID")
    private Integer JDID;

    @TableId(value = "ID", type = IdType.AUTO)
    @JSONField(name="ID")
    private Integer ID;

    @JSONField(name="R_Condition")
    private String R_Condition;

    @JSONField(name="L_Condition_Code")
    private String L_Condition_Code;


    @Override
    protected Serializable pkVal() {
        return this.ID;
    }

}
