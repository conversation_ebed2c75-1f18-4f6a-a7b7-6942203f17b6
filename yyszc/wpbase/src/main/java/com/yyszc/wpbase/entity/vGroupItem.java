package com.yyszc.wpbase.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * vGroupItem
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("vGroupItem")
@ApiModel(value="vGroupItem对象", description="vGroupItem")
public class vGroupItem extends Model<vGroupItem> {

    private static final long serialVersionUID = 1L;

    @JSONField(name="dydj")
    private String dydj;

    @JSONField(name="parentid")
    private Integer parentid;

    @JSONField(name="plevel")
    private Integer plevel;

    @JSONField(name="ParentTPath")
    private String ParentTPath;

    @JSONField(name="id")
    private Integer id;

    @JSONField(name="ParentName")
    private String ParentName;

    @JSONField(name="Category")
    private Integer Category;

    @JSONField(name="TopGroupName")
    private String TopGroupName;

    @JSONField(name="ParentPath")
    private String ParentPath;

    @JSONField(name="TopGroupId")
    private Integer TopGroupId;

    @JSONField(name="UComapanyQC")
    private String UComapanyQC;

    @JSONField(name="type")
    private Integer type;

    @JSONField(name="XH")
    private Integer XH;

    @JSONField(name="IsShow")
    private Integer IsShow;

    @JSONField(name="shortpinyin")
    private String shortpinyin;

    @JSONField(name="GroupDesc")
    private String GroupDesc;

    @JSONField(name="groupname")
    private String groupname;


    @Override
    protected Serializable pkVal() {
        return null;
    }

}
