package com.yyszc.wpbase.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("Lcdefine")
@ApiModel(value="Lcdefine对象", description="")
public class Lcdefine extends Model<Lcdefine> {

    private static final long serialVersionUID = 1L;

    @JSONField(name="app_url")
    private String app_url;

    @JSONField(name="LcID")
    private Integer LcID;

    @JSONField(name="ywUrl")
    private String ywUrl;

    @JSONField(name="ywb")
    private String ywb;

    @TableId(value = "ID", type = IdType.AUTO)
    @JSONField(name="ID")
    private Integer ID;

    @JSONField(name="isUse")
    private Integer isUse;

    @JSONField(name="xszd")
    private String xszd;

    @JSONField(name="app_ywb")
    private String app_ywb;

    @JSONField(name="lcName")
    private String lcName;


    @Override
    protected Serializable pkVal() {
        return this.ID;
    }

}
