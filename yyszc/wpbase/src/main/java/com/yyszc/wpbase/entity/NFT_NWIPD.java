package com.yyszc.wpbase.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 导航信息表--内网认定表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("NFT_NWIPD")
@ApiModel(value="NFT_NWIPD对象", description="导航信息表--内网认定表")
public class NFT_NWIPD extends Model<NFT_NWIPD> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "标识")
    @TableId(value = "ID", type = IdType.AUTO)
    @JSONField(name="ID")
    private Integer ID;

    @ApiModelProperty(value = "网段标识")
    @JSONField(name="IPW")
    private String IPW;

    @ApiModelProperty(value = "起点地址")
    @JSONField(name="IPS")
    private Integer IPS;

    @ApiModelProperty(value = "终结地址")
    @JSONField(name="IPE")
    private Integer IPE;


    @Override
    protected Serializable pkVal() {
        return this.ID;
    }

}
