package com.yyszc.wpbase.ventity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;

@Data
@NoArgsConstructor
public class RoleEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "Id")
    @TableId(value = "Id", type = IdType.AUTO)
    @JSONField(name="Id")
    private Integer Id;

    @ApiModelProperty(value = "IsHide")
    @JSONField(name="IsHide")
    private Integer IsHide;

    @ApiModelProperty(value = "RoleName")
    @JSONField(name="RoleName")
    private String RoleName;

    @ApiModelProperty(value = "AdminGroupIds")
    @JSONField(name="AdminGroupIds")
    private String AdminGroupIds;

    @ApiModelProperty(value = "RoleKind")
    @JSONField(name="RoleKind")
    private String RoleKind;
}
