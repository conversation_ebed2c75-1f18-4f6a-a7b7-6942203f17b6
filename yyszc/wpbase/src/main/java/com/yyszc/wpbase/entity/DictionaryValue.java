package com.yyszc.wpbase.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("DictionaryValue")
@ApiModel(value="DictionaryValue对象", description="")
public class DictionaryValue extends Model<DictionaryValue> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.AUTO)
    @JSONField(name="ID")
    private Integer ID;

    @JSONField(name="Parameter")
    private String Parameter;

    @JSONField(name="IsUsed")
    private Integer IsUsed;

    @JSONField(name="TitleID")
    private Integer TitleID;

    @JSONField(name="Content")
    private String Content;


    @Override
    protected Serializable pkVal() {
        return this.ID;
    }

}
