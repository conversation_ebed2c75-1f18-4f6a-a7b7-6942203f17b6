package com.yyszc.wpbase.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("GroupItem")
@ApiModel(value="GroupItem对象", description="")
public class GroupItem extends Model<GroupItem> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "单位编号")
    @JSONField(name="uComapanyBH")
    private String uComapanyBH;

    @ApiModelProperty(value = "单位简称")
    @JSONField(name="uComapanyJC")
    private String uComapanyJC;

    @ApiModelProperty(value = "序号")
    @JSONField(name="XH")
    private Integer XH;

    @ApiModelProperty(value = "是否（分包）")
    @JSONField(name="IsShow")
    private Integer IsShow;

    @ApiModelProperty(value = "类型（1：单位；2：部门；3：班组；4：其它）")
    @JSONField(name="Category")
    private Integer Category;

    @JSONField(name="dydj")
    private String dydj;

    @JSONField(name="parentid")
    private Integer parentid;

    @TableId(value = "id", type = IdType.AUTO)
    @JSONField(name="id")
    private Integer id;

    @JSONField(name="parentidRz")
    private Integer parentidRz;

    @JSONField(name="uComapanyQC")
    private String uComapanyQC;

    @JSONField(name="type")
    private Integer type;

    @JSONField(name="groupname")
    private String groupname;

    @JSONField(name="shortpinyin")
    private String shortpinyin;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
