package com.yyszc.wpbase.ventity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
public class PersonEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "Id")
    @TableId(value = "Id", type = IdType.AUTO)
    @JSONField(name="Id")
    private Integer Id;

    @ApiModelProperty(value = "LoginName")
    @JSONField(name="LoginName")
    private String LoginName;

    @ApiModelProperty(value = "RealName")
    @JSONField(name="RealName")
    private String RealName;

    @ApiModelProperty(value = "Password")
    @JSONField(name="Password")
    private String Password;

    @ApiModelProperty(value = "RoleId")
    @JSONField(name="RoleId")
    private Integer RoleId;

    @ApiModelProperty(value = "Telephone")
    @JSONField(name="Telephone")
    private String Telephone;

    @ApiModelProperty(value = "MsgType")
    @JSONField(name="MsgType")
    private String MsgType;

    @ApiModelProperty(value = "OA")
    @JSONField(name="OA")
    private String OA;

    @ApiModelProperty(value = "type")
    @JSONField(name="type")
    private Integer type;

    @ApiModelProperty(value = "P_XH")
    @JSONField(name="P_XH")
    private Integer P_XH;

    @ApiModelProperty(value = "RolePermissionString")
    @JSONField(name="RolePermissionString")
    private String RolePermissionString;

    @ApiModelProperty(value = "RoleIdsString")
    @JSONField(name="RoleIdsString")
    private String RoleIdsString;

    @ApiModelProperty(value = "RoleNamesString")
    @JSONField(name="RoleNamesString")
    private String RoleNamesString;

    @ApiModelProperty(value = "GroupId")
    @JSONField(name="GroupId")
    private Integer GroupId;

    @ApiModelProperty(value = "GroupType")
    @JSONField(name="GroupType")
    private Integer GroupType;

    @ApiModelProperty(value = "ParentGroupId")
    @JSONField(name="ParentGroupId")
    private Integer ParentGroupId;

    @ApiModelProperty(value = "TopGroupId")
    @JSONField(name="TopGroupId")
    private Integer TopGroupId;

    @ApiModelProperty(value = "GroupName")
    @JSONField(name="GroupName")
    private String GroupName;

    @ApiModelProperty(value = "ParentGroupName")
    @JSONField(name="ParentGroupName")
    private String ParentGroupName;

    @ApiModelProperty(value = "TopGroupName")
    @JSONField(name="TopGroupName")
    private String TopGroupName;

    @ApiModelProperty(value = "GroupNameS")
    @JSONField(name="GroupNameS")
    private String GroupNameS;

    @ApiModelProperty(value = "ParentGroupNameS")
    @JSONField(name="ParentGroupNameS")
    private String ParentGroupNameS;

    @ApiModelProperty(value = "TopGroupNameS")
    @JSONField(name="TopGroupNameS")
    private String TopGroupNameS;

    @ApiModelProperty(value = "RoleList")
    @JSONField(name="RoleList")
    private ArrayList<String> RoleList;

    @ApiModelProperty(value = "PermissionList")
    @JSONField(name="PermissionList")
    private ArrayList<String> PermissionList;

    @ApiModelProperty(value = "NewEmailNum")
    @JSONField(name="NewEmailNum")
    private String NewEmailNum;

    @ApiModelProperty(value = "EmailNum")
    @JSONField(name="EmailNum")
    private String EmailNum;

    /// <summary>
    ///内外网判断 1是内网，其他是外网
    /// </summary>
    @ApiModelProperty(value = "pwf")
    @JSONField(name="pwf")
    private int pwf;

    /// <summary>
    /// 名字图片
    /// </summary>
    @ApiModelProperty(value = "PhName")
    @JSONField(name="PhName")
    private int PhName;


    /// <summary>
    ///专家抽取次数
    /// </summary>
    @ApiModelProperty(value = "ZJ_CS")
    @JSONField(name="ZJ_CS")
    private int ZJ_CS;

    //施工单位的基本信息
    /// <summary>
    /// 申报对象单位名称
    /// </summary>
    @ApiModelProperty(value = "parentunit")
    @JSONField(name="parentunit")
    private String parentunit;

    /// <summary>
    /// 判断系统是否能手动输入身份证
    /// </summary>
    @ApiModelProperty(value = "IsOpen")
    @JSONField(name="IsOpen")
    private String IsOpen;

    /// <summary>
    /// 判断是否是子账号
    /// </summary>
    @ApiModelProperty(value = "IsMain")
    @JSONField(name="IsMain")
    private String IsMain;


    /// <summary>
    /// 单位名称
    /// </summary>
    @ApiModelProperty(value = "unit")
    @JSONField(name="unit")
    private String unit;

    /// <summary>
    /// 入围联系人
    /// </summary>
    @ApiModelProperty(value = "ucontactname")
    @JSONField(name="ucontactname")
    private String ucontactname;

    /// <summary>
    /// 入围联系人身份证
    /// </summary>
    @ApiModelProperty(value = "ucontactid")
    @JSONField(name="ucontactid")
    private String ucontactid;

    /// <summary>
    /// 集团企业编号
    /// </summary>
    @ApiModelProperty(value = "uComapanyBH")
    @JSONField(name="uComapanyBH")
    private String uComapanyBH;

    /// <summary>
    /// 集团企业简称
    /// </summary>
    @ApiModelProperty(value = "uComapanyJC")
    @JSONField(name="uComapanyJC")
    private String uComapanyJC;


    /// <summary>
    /// 集团企业简称
    /// </summary>
    @ApiModelProperty(value = "uComapanyQC")
    @JSONField(name="uComapanyQC")
    private String uComapanyQC;

    /// <summary>
    /// 组织机构前八位
    /// </summary>
    @ApiModelProperty(value = "zzCode")
    @JSONField(name="zzCode")
    private String zzCode;

    /// <summary>
    /// 工程类型 0土建 1电气 2通信 3设计
    /// </summary>
    @ApiModelProperty(value = "GCType")
    @JSONField(name="GCType")
    private String GCType;

    /// <summary>
    /// 管辖单位Id字符串组合，如 1,3,19
    /// </summary>
    @ApiModelProperty(value = "AdminGroupBHs")
    @JSONField(name="AdminGroupBHs")
    private String AdminGroupBHs;

    /// <summary>
    /// I_UserData表ID
    /// </summary>
    @ApiModelProperty(value = "Uid")
    @JSONField(name="Uid")
    private int Uid;

    /// <summary>
    /// I_UserData表utype
    /// </summary>
    @ApiModelProperty(value = "UType")
    @JSONField(name="UType")
    private String UType;

    /// <summary>
    /// T_WorkFlowStart
    /// </summary>
    @ApiModelProperty(value = "WorkFlowStart")
    @JSONField(name="WorkFlowStart")
    private int WorkFlowStart;

    /// <summary>
    /// T_WorkFlowBackId
    /// </summary>
    @ApiModelProperty(value = "WorkFlowBackId")
    @JSONField(name="WorkFlowBackId")
    private int WorkFlowBackId;

    /// <summary>
    /// T_WorkFlowIsHg
    /// </summary>
    @ApiModelProperty(value = "WorkFlowIsHg")
    @JSONField(name="WorkFlowIsHg")
    private int WorkFlowIsHg;

    /// <summary>
    /// uyearrge
    /// </summary>
    @ApiModelProperty(value = "Uyesrrge")
    @JSONField(name="Uyesrrge")
    private String Uyesrrge;

    /// <summary>
    /// SumNum 法人项目类型总数量
    /// </summary>
    @ApiModelProperty(value = "SumNum")
    @JSONField(name="SumNum")
    private String SumNum;

    /// <summary>
    /// I_UserData表zIntegval，积分
    /// </summary>
    @ApiModelProperty(value = "zIntegval")
    @JSONField(name="zIntegval")
    private int zIntegval;

    @ApiModelProperty(value = "isdzqm")
    @JSONField(name="isdzqm")
    private String isdzqm;

    @ApiModelProperty(value = "RoleStr")
    @JSONField(name="RoleStr")
    private String RoleStr;

    @ApiModelProperty(value = "LoginName2")
    @JSONField(name="LoginName2")
    private String LoginName2;

    @ApiModelProperty(value = "xmbID")
    @JSONField(name="xmbID")
    private int xmbID;

    @ApiModelProperty(value = "xmbName")
    @JSONField(name="xmbName")
    private String xmbName;

    @ApiModelProperty(value = "Sphone")
    @JSONField(name="Sphone")
    private String Sphone;


    @ApiModelProperty(value = "RealNameLoginName")
    @JSONField(name="RealNameLoginName")
    private String RealNameLoginName;

    @ApiModelProperty(value = "state")
    @JSONField(name="state")
    private int state;

    @ApiModelProperty(value = "RoleName")
    @JSONField(name="RoleName")
    private String RoleName;

    @ApiModelProperty(value = "TopRzGroupName")
    @JSONField(name="TopRzGroupName")
    private String TopRzGroupName;

    @ApiModelProperty(value = "TopGroupShortPinyin")
    @JSONField(name="TopGroupShortPinyin")
    private String TopGroupShortPinyin;

    @ApiModelProperty(value = "TopRzGroupId")
    @JSONField(name="TopRzGroupId")
    private int TopRzGroupId;

    @ApiModelProperty(value = "FWQID")
    @JSONField(name="FWQID")
    private int FWQID;

    @ApiModelProperty(value = "FWQName")
    @JSONField(name="FWQName")
    private String FWQName;

    @ApiModelProperty(value = "ParentRzGroupId")
    @JSONField(name="ParentRzGroupId")
    private int ParentRzGroupId;

    @ApiModelProperty(value = "ParentZGroupId")
    @JSONField(name="ParentZGroupId")
    private int ParentZGroupId;

    @ApiModelProperty(value = "ParentZGroupName")
    @JSONField(name="ParentZGroupName")
    private String ParentZGroupName;

    @ApiModelProperty(value = "AdminRzGroupIds")
    @JSONField(name="AdminRzGroupIds")
    private String AdminGroupIds;

    @ApiModelProperty(value = "AdminRzGroupIds")
    @JSONField(name="AdminRzGroupIds")
    private String AdminRzGroupIds;

    @ApiModelProperty(value = "Roles")
    @JSONField(name="Roles")
    private List<RoleEntity> Roles;

    @ApiModelProperty(value = "DateTimeString")
    @JSONField(name="DateTimeString")
    private String DateTimeString;

    @ApiModelProperty(value = "ChoseNF")
    @JSONField(name="ChoseNF")
    private String ChoseNF;

    @ApiModelProperty(value = "HaveFB")
    @JSONField(name="HaveFB")
    private String HaveFB;

    @ApiModelProperty(value = "RoleKing")
    @JSONField(name="RoleKing")
    private String RoleKing;

    @ApiModelProperty(value = "CertificateID")
    @JSONField(name="CertificateID")
    private String CertificateID;

    @ApiModelProperty(value = "OfficePhone")
    @JSONField(name="OfficePhone")
    private String OfficePhone;

    @ApiModelProperty(value = "HeadIco")
    @JSONField(name="HeadIco")
    private String HeadIco;

    public boolean isAdmin() {
        if(LoginName.equals("admin")||RoleNamesString.indexOf("系统管理")!=-1){
            return true;
        }
        return false;
    }
}
