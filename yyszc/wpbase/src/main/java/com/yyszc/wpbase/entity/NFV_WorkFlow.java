package com.yyszc.wpbase.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 导航信息视图==待办流程使用视图
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("NFV_WorkFlow")
@ApiModel(value="NFV_WorkFlow对象", description="导航信息视图==待办流程使用视图")
public class NFV_WorkFlow extends Model<NFV_WorkFlow> {

    private static final long serialVersionUID = 1L;

    @JSONField(name="LcName")
    private String LcName;

    @JSONField(name="LcProjName")
    private String LcProjName;

    @JSONField(name="BXTYPE")
    private String BXTYPE;

    @JSONField(name="days")
    private Integer days;

    @JSONField(name="lc_jdmc")
    private String lc_jdmc;

    @JSONField(name="lc_defineID")
    private Integer lc_defineID;

    @JSONField(name="transdate")
    private LocalDateTime transdate;

    @JSONField(name="PNO")
    private String PNO;

    @JSONField(name="sendGroupIDs")
    private String sendGroupIDs;

    @JSONField(name="LcProjUrl")
    private String LcProjUrl;

    @JSONField(name="LcCount")
    private Integer LcCount;

    @JSONField(name="ywID")
    private Integer ywID;

    @JSONField(name="personName")
    private String personName;

    @JSONField(name="cbkstyle")
    private String cbkstyle;

    @JSONField(name="sendPerson")
    private String sendPerson;

    @JSONField(name="feed")
    private String feed;

    @JSONField(name="LcByRole")
    private Integer LcByRole;

    @JSONField(name="ID")
    private Integer ID;

    @JSONField(name="LcProjParam")
    private String LcProjParam;

    @JSONField(name="wfexists")
    private Integer wfexists;

    @JSONField(name="sendPersonZgh")
    private String sendPersonZgh;

    @JSONField(name="lc_jdid")
    private Integer lc_jdid;

    @JSONField(name="startdate")
    private LocalDateTime startdate;


    @Override
    protected Serializable pkVal() {
        return null;
    }

}
