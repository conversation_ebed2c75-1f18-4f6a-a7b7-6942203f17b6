package com.yyszc.wpbase.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * vDictionaryValue
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("vDictionaryValue")
@ApiModel(value="vDictionaryValue对象", description="vDictionaryValue")
public class vDictionaryValue extends Model<vDictionaryValue> {

    private static final long serialVersionUID = 1L;

    @JSONField(name="TitleID")
    private Integer TitleID;

    @JSONField(name="IsUsed")
    private Integer IsUsed;

    @JSONField(name="Content")
    private String Content;

    @JSONField(name="Title")
    private String Title;

    @JSONField(name="ID")
    private Integer ID;

    @JSONField(name="Parameter")
    private String Parameter;


    @Override
    protected Serializable pkVal() {
        return null;
    }

}
