package com.yyszc.wpbase.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("Lc_currentState")
@ApiModel(value="Lc_currentState对象", description="")
public class Lc_currentState extends Model<Lc_currentState> {

    private static final long serialVersionUID = 1L;

    @JSONField(name="lc_jdid")
    private Integer lc_jdid;

    @JSONField(name="AllPersonZgh")
    private String AllPersonZgh;

    @JSONField(name="PNO")
    private String PNO;

    @JSONField(name="isOtherAdd")
    private Integer isOtherAdd;

    @JSONField(name="Lc_defineID")
    private Integer Lc_defineID;

    @JSONField(name="sendPerson")
    private String sendPerson;

    @JSONField(name="number")
    private Integer number;

    @JSONField(name="isMany")
    private Integer isMany;

    @JSONField(name="lc_isback")
    private Integer lc_isback;

    @JSONField(name="sendGroupIDs")
    private String sendGroupIDs;

    @JSONField(name="sendPersonZgh")
    private String sendPersonZgh;

    @JSONField(name="lc_jdmc")
    private String lc_jdmc;

    @JSONField(name="Lc_Name")
    private String Lc_Name;

    @JSONField(name="lc_tojdid")
    private String lc_tojdid;

    @JSONField(name="BXType")
    private String BXType;

    @TableId(value = "ID", type = IdType.AUTO)
    @JSONField(name="ID")
    private Integer ID;

    @JSONField(name="ywID")
    private Integer ywID;


    @Override
    protected Serializable pkVal() {
        return this.ID;
    }

}
