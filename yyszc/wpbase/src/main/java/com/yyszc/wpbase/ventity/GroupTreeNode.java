package com.yyszc.wpbase.ventity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * vGroupItem
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-03
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("GroupTreeNode")
@ApiModel(value="GroupTreeNode", description="GroupTreeNode")
public class GroupTreeNode extends Model<GroupTreeNode> {

    private static final long serialVersionUID = 1L;

    @JSONField(name="dydj")
    private String dydj;

    @JSONField(name="parentid")
    private Integer parentid;

    @JSONField(name="plevel")
    private Integer plevel;

    @JSONField(name="ParentTPath")
    private String ParentTPath;

    @JSONField(name="id")
    private Integer id;

    @JSONField(name="ParentName")
    private String ParentName;

    @JSONField(name="Category")
    private Integer Category;

    @JSONField(name="TopGroupName")
    private String TopGroupName;

    @JSONField(name="ParentPath")
    private String ParentPath;

    @JSONField(name="TopGroupId")
    private Integer TopGroupId;

    @JSONField(name="UComapanyQC")
    private String UComapanyQC;

    @JSONField(name="type")
    private Integer type;

    @JSONField(name="XH")
    private Integer XH;

    @JSONField(name="IsShow")
    private Integer IsShow;

    @JSONField(name="shortpinyin")
    private String shortpinyin;

    @JSONField(name="GroupDesc")
    private String GroupDesc;

    @JSONField(name="groupname")
    private String groupname;

    @JSONField(name="ChildList")
    private List<GroupTreeNode> ChildList;

    @Override
    protected Serializable pkVal() {
        return null;
    }

}
