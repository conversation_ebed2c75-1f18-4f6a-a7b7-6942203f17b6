package com.yyszc.wpbase.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("RolePerson")
@ApiModel(value="RolePerson对象", description="")
public class RolePerson extends Model<RolePerson> {

    private static final long serialVersionUID = 1L;

    @J<PERSON>NField(name="RoleId")
    private Integer RoleId;

    @JSONField(name="PersonId")
    private Integer PersonId;

    @JSONField(name="Id")
    private Integer Id;


    @Override
    protected Serializable pkVal() {
        return null;
    }

}
