package com.yyszc.wpbase.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("Lc_workFlow")
@ApiModel(value="Lc_workFlow对象", description="")
public class Lc_workFlow extends Model<Lc_workFlow> {

    private static final long serialVersionUID = 1L;

    @JSONField(name="groupName")
    private String groupName;

    @JSONField(name="transdate")
    private LocalDateTime transdate;

    @JSONField(name="BXType")
    private String BXType;

    @JSONField(name="LcByRole")
    private Integer LcByRole;

    @JSONField(name="lc_jdmc")
    private String lc_jdmc;

    @JSONField(name="lc_defineID")
    private Integer lc_defineID;

    @JSONField(name="feed")
    private String feed;

    @JSONField(name="PNO")
    private String PNO;

    @JSONField(name="personZgh")
    private String personZgh;

    @JSONField(name="isback")
    private Integer isback;

    @JSONField(name="personName")
    private String personName;

    @JSONField(name="ywID")
    private Integer ywID;

    @JSONField(name="groupID")
    private Integer groupID;

    @JSONField(name="startdate")
    private LocalDateTime startdate;

    @JSONField(name="number")
    private Integer number;

    @JSONField(name="useback")
    private Integer useback;

    @TableId(value = "ID", type = IdType.AUTO)
    @JSONField(name="ID")
    private Integer ID;

    @JSONField(name="lc_jdID")
    private Integer lc_jdID;


    @Override
    protected Serializable pkVal() {
        return this.ID;
    }

}
