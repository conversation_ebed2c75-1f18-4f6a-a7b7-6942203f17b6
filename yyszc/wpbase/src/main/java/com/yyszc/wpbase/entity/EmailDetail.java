package com.yyszc.wpbase.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("EmailDetail")
@ApiModel(value="EmailDetail对象", description="")
public class EmailDetail extends Model<EmailDetail> {

    private static final long serialVersionUID = 1L;

    @JSONField(name="personGroupName")
    private String personGroupName;

    @JSONField(name="HisContent")
    private String HisContent;

    @JSONField(name="Title")
    private String Title;

    @JSONField(name="personZgh")
    private String personZgh;

    @TableId(value = "ID", type = IdType.AUTO)
    @JSONField(name="ID")
    private Integer ID;

    @JSONField(name="StarLevel")
    private Integer StarLevel;

    @JSONField(name="IsDelete")
    private Integer IsDelete;

    @JSONField(name="State")
    private String State;

    @JSONField(name="Content")
    private String Content;

    @JSONField(name="EmailID")
    private Integer EmailID;

    @JSONField(name="personGroupID")
    private Integer personGroupID;

    @JSONField(name="Type")
    private Integer Type;

    @JSONField(name="personName")
    private String personName;


    @Override
    protected Serializable pkVal() {
        return this.ID;
    }

}
