package com.yyszc.wpbase.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 模块--模块自动启动菜单项
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("NFV_ModuleAMLink")
@ApiModel(value="NFV_ModuleAMLink对象", description="模块--模块自动启动菜单项")
public class NFV_ModuleAMLink extends Model<NFV_ModuleAMLink> {

    private static final long serialVersionUID = 1L;

    @JSONField(name="module_name")
    private String module_name;

    @JSONField(name="LK_TPATH")
    private String LK_TPATH;

    @JSONField(name="LK_ID")
    private Integer LK_ID;

    @JSONField(name="LK_MID")
    private Integer LK_MID;

    @JSONField(name="LK_TNODE")
    private String LK_TNODE;

    @JSONField(name="LK_TMENU")
    private String LK_TMENU;

    @JSONField(name="module_num")
    private String module_num;


    @Override
    protected Serializable pkVal() {
        return null;
    }

}
