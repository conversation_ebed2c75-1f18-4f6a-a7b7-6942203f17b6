package com.yyszc.wpbase.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("v$EmailDetail")
@ApiModel(value="v$EmailDetail对象", description="")
public class v$EmailDetail extends Model<v$EmailDetail> {

    private static final long serialVersionUID = 1L;

    @JSONField(name="Content")
    private String Content;

    @JSONField(name="EmailID")
    private Integer EmailID;

    @JSONField(name="personGroupID")
    private Integer personGroupID;

    @JSONField(name="Type")
    private Integer Type;

    @JSONField(name="Sdate")
    private LocalDateTime Sdate;

    @JSONField(name="personName")
    private String personName;

    @JSONField(name="personGroupName")
    private String personGroupName;

    @JSONField(name="HisContent")
    private String HisContent;

    @JSONField(name="Title")
    private String Title;

    @JSONField(name="personZgh")
    private String personZgh;

    @JSONField(name="ID")
    private Integer ID;

    @JSONField(name="StarLevel")
    private Integer StarLevel;

    @JSONField(name="IsDelete")
    private Integer IsDelete;

    @JSONField(name="State")
    private String State;


    @Override
    protected Serializable pkVal() {
        return null;
    }

}
