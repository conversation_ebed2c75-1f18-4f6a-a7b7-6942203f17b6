package com.yyszc.wpbase.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 模块--模块自动启动菜单项
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("NFT_ModuleAMLink")
@ApiModel(value="NFT_ModuleAMLink对象", description="模块--模块自动启动菜单项")
public class NFT_ModuleAMLink extends Model<NFT_ModuleAMLink> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "记录编号")
    @TableId(value = "LK_ID", type = IdType.AUTO)
    @JSONField(name="LK_ID")
    private Integer LK_ID;

    @ApiModelProperty(value = "模块ID")
    @JSONField(name="LK_MID")
    private Integer LK_MID;

    @ApiModelProperty(value = "菜单根")
    @JSONField(name="LK_TMENU")
    private String LK_TMENU;

    @ApiModelProperty(value = "菜单子项")
    @JSONField(name="LK_TPATH")
    private String LK_TPATH;

    @ApiModelProperty(value = "菜单项")
    @JSONField(name="LK_TNODE")
    private String LK_TNODE;


    @Override
    protected Serializable pkVal() {
        return this.LK_ID;
    }

}
