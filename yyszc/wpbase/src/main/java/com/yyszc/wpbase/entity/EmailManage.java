package com.yyszc.wpbase.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("EmailManage")
@ApiModel(value="EmailManage对象", description="")
public class EmailManage extends Model<EmailManage> {

    private static final long serialVersionUID = 1L;

    @JSONField(name="Lc_jdmc")
    private String Lc_jdmc;

    @JSONField(name="Sperson")
    private String Sperson;

    @JSONField(name="Sgroupid")
    private Integer Sgroupid;

    @JSONField(name="YWTab")
    private String YWTab;

    @JSONField(name="Title")
    private String Title;

    @JSONField(name="IsDelete")
    private Integer IsDelete;

    @JSONField(name="CsPersonAll")
    private String CsPersonAll;

    @JSONField(name="Zxmc")
    private String Zxmc;

    @JSONField(name="RpersonZgh")
    private String RpersonZgh;

    @JSONField(name="ZxmcID")
    private String ZxmcID;

    @JSONField(name="QGdh")
    private String QGdh;

    @JSONField(name="YWUrl")
    private String YWUrl;

    @JSONField(name="Lc_defineID")
    private String Lc_defineID;

    @JSONField(name="Rdate")
    private LocalDateTime Rdate;

    @JSONField(name="CsPerson")
    private String CsPerson;

    @JSONField(name="SpersonZgh")
    private String SpersonZgh;

    @JSONField(name="Sdate")
    private LocalDateTime Sdate;

    @JSONField(name="StarLevel")
    private Integer StarLevel;

    @JSONField(name="Xsjhdh")
    private String Xsjhdh;

    @JSONField(name="XsjhdmcID")
    private String XsjhdmcID;

    @JSONField(name="Content")
    private String Content;

    @JSONField(name="RpersonAll")
    private String RpersonAll;

    @JSONField(name="YWJS")
    private String YWJS;

    @JSONField(name="Sgroupname")
    private String Sgroupname;

    @JSONField(name="WWdh")
    private String WWdh;

    @TableId(value = "ID", type = IdType.AUTO)
    @JSONField(name="ID")
    private Integer ID;

    @JSONField(name="Lc_jdID")
    private Integer Lc_jdID;

    @JSONField(name="Rperson")
    private String Rperson;

    @JSONField(name="XmmcID")
    private String XmmcID;

    @JSONField(name="State")
    private String State;

    @JSONField(name="CsPersonZgh")
    private String CsPersonZgh;

    @JSONField(name="YWID")
    private Integer YWID;

    @JSONField(name="Xmmc")
    private String Xmmc;

    @JSONField(name="HisContent")
    private String HisContent;

    @JSONField(name="Scjhdh")
    private String Scjhdh;

    @JSONField(name="ScjhmcID")
    private String ScjhmcID;


    @Override
    protected Serializable pkVal() {
        return this.ID;
    }

}
