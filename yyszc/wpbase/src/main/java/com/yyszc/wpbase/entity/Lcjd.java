package com.yyszc.wpbase.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("Lcjd")
@ApiModel(value="Lcjd对象", description="")
public class Lcjd extends Model<Lcjd> {

    private static final long serialVersionUID = 1L;

    @JSONField(name="LookFileJdID")
    private String LookFileJdID;

    @JSONField(name="CheckData")
    private String CheckData;

    @TableId(value = "ID", type = IdType.AUTO)
    @JSONField(name="ID")
    private Integer ID;

    @JSONField(name="IsBX")
    private Integer IsBX;

    @JSONField(name="lc_defineID")
    private Integer lc_defineID;

    @JSONField(name="type")
    private Integer type;

    @JSONField(name="nextID")
    private Integer nextID;

    @JSONField(name="lcjdID")
    private Integer lcjdID;

    @JSONField(name="CheckFile")
    private String CheckFile;

    @JSONField(name="BackjdID")
    private String BackjdID;

    @JSONField(name="GroupID")
    private String GroupID;

    @JSONField(name="shgr")
    private Integer shgr;

    @JSONField(name="FormType")
    private String FormType;

    @JSONField(name="jdmc")
    private String jdmc;

    @JSONField(name="canBack")
    private Integer canBack;


    @Override
    protected Serializable pkVal() {
        return this.ID;
    }

}
