package com.yyszc.extend;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;

@SuppressWarnings("unchecked")
public class DataTable extends ExObject implements Serializable,Iterable<DataRow> {
    private String tableName;
    private boolean readOnly = false;
    private List<DataRow> rowList = new ArrayList<DataRow>();
    private List<DataColumn> colList = new ArrayList<DataColumn>();

    public DataTable() {

    }

    public DataTable(String dataTableName) {
        this.tableName = dataTableName;
    }

    public List<DataRow> getRowList() {
        return rowList;
    }

    public void setRowList(List<DataRow> rowList) {
        this.rowList = rowList;
    }

    public List<DataColumn> getColList() {
        return colList;
    }

    public void setColList(List<DataColumn> colList) {
        this.colList = colList;
    }

    public int getTotalCount() {
        return rowList.size();
    }

    public boolean isReadOnly() {
        return this.readOnly;
    }

    public void setReadOnly(boolean readOnly) {
        this.readOnly = readOnly;
    }

    public String getTableName() {
        return this.tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public Object getCell(int row, String colName) {
        return this.rowList.get(row).getColValue(colName);
    }

    public Object getCell(int row, int col) {
        return this.rowList.get(row).getColValue(col);
    }

    public void setCell(int row, int col, Object value) {
        this.rowList.get(row).setColValue(col, value);
    }

    public void setCell(int row, String colName, Object value) {
        this.rowList.get(row).setColValue(colName, value);
    }

    public Class getRowType() {
        return this.rowList.get(0).getClass();
    }

    public Object getRowValue(int index) {
        return this.rowList.get(index);
    }

    public void addRow(DataRow dr) {
        if (containKey(dr)) {
            removeRow(dr);
        }
        rowList.add(dr);
    }

    public void addRow(int index, DataRow dr) {
        if (containKey(dr)) {
            removeRow(dr);
        }
        rowList.add(index, dr);
    }

    public boolean containKey(DataRow dr) {
        return (-1 != rowList.indexOf(dr));
    }

    public void addAll(Collection cc) {
        removeAll(cc);
        rowList.addAll(cc);
    }

    public void addAll(int index, Collection cc) {
        removeAll(cc);
        rowList.addAll(index, cc);
    }

    public DataRow getRow(int index) {
        return rowList.get(index);
    }

    public void removeAll(Collection<DataRow> cc) {
        for (DataRow dr : cc) {
            removeRow(dr);
        }
    }

    public void removeRow(DataRow dr) {
        rowList.remove(rowList.indexOf(dr));
    }

    public void clear() {
        rowList.clear();
    }

    public int size() {
        return rowList.size();
    }

    public boolean isEmpty() {
        return rowList.isEmpty();
    }

    @Override
    public Iterator<DataRow> iterator() {
        return rowList.iterator();
    }

    public String toJsonString(){
        StringBuilder retstr=new StringBuilder();
        retstr.append("[");
        for(int i=0;i<this.getRowList().size();i++)
        {
            DataRow dr=this.getRow(i);
            retstr.append("{");
            for(int j=0;j<dr.getColumnList().size();j++)
            {
                DataColumn dc=dr.getColumn(j);
                String cname=dc.getColumnName();
                String cvalue=dc.getColumnValue()==null?"":dc.getColumnValue().toString();

                if(j<dr.getColumnList().size()-1)
                {
                    retstr.append("\""+cname+"\":\""+cvalue+"\",");
                }else
                {
                    retstr.append("\""+cname+"\":\""+cvalue+"\"");
                }
            }
            if(i<this.getRowList().size()-1)
            {
                retstr.append("},");
            }else
            {
                retstr.append("}");
            }
        }
        retstr.append("]");
        return retstr.toString();
    }
}
