package com.yyszc.extend;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;

public class DataRow extends ExObject implements Iterable<DataColumn>{
    private List<DataColumn> columnList = new ArrayList<DataColumn>();

    public DataRow() {
    }

    public List<DataColumn> getColumnList() {
        return columnList;
    }

    public void setColumnList(List<DataColumn> columnList) {
        this.columnList = columnList;
    }

    public void setColValue(int index, Object value) {
        setColValue(this.columnList.get(index), value);
    }

    public void setColValue(String columnName, Object value) {
        setColValue(getColumn(columnName),value);
    }

    public void setColValue(DataColumn column, Object value) {
        if (column != null) {
            int index=columnList.indexOf(column);
            if(index!=-1) {
                column.setColumnValue(value);
            }
        }
    }

    public Object getColValue(int index) {
        return getColumn(index).getColumnValue();
    }

    public Object getColValue(String colname) {
        DataColumn dc=getColumn(colname);
        if(dc!=null) {
            return dc.getColumnValue();
        } else {
            return new String("");
        }
    }

    public void copyFrom(DataRow row) {
        this.columnList.clear();

        for (DataColumn dc : row.columnList) {
            this.columnList.add(dc);
        }
    }

    public void addColumn(DataColumn dc) {
        if (containKey(dc)) {
            remove(dc);
        }
        columnList.add(dc);
    }

    public void addColumn(int index, DataColumn dc) {
        if (containKey(dc)) {
            remove(dc);
        }
        columnList.add(index, dc);
    }

    public void addAll(Collection<DataColumn> cc) {
        removeAll(cc);
        columnList.addAll(cc);
    }

    public void addAll(int index, Collection<DataColumn> cc) {
        removeAll(cc);
        columnList.addAll(index, cc);
    }

    public DataColumn getColumn(int index) {
        return columnList.get(index);
    }

    public DataColumn getColumn(String colname) {
        for (DataColumn dc:columnList) {
            if(dc.getColumnName().equals(colname)) {
                return dc;
            }
        }
        return null;
    }

    public boolean containKey(DataColumn dc) {
        return (-1 != columnList.indexOf(dc));
    }

    public void removeAll(Collection<DataColumn> cc) {
        for (DataColumn dc : cc) {
            remove(dc);
        }
    }

    public void clear() {
        columnList.clear();
    }

    public void remove(DataColumn dc) {
        columnList.remove(columnList.indexOf(dc));
    }

    public int size() {
        return columnList.size();
    }

    public boolean isEmpty() {
        return columnList.isEmpty();
    }

    public Iterator<DataColumn> iterator() {
        return columnList.iterator();
    }

}
