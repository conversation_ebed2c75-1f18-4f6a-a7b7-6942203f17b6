package com.yyszc.extend;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;

public class DataSet extends ExObject implements Iterable<DataTable> {
    private List<DataTable> tableList = new ArrayList<DataTable>();

    public DataSet() {

    }

    public List<DataTable> getTableList() {
        return tableList;
    }

    public void setTableList(List<DataTable> tableList) {
        this.tableList = tableList;
    }

    public void addTable(DataTable table) {
        if (containKey(table)) {
            removeTable(table);
        }
        tableList.add(table);
    }

    public boolean containKey(DataTable table) {
        return (-1 != tableList.indexOf(table));
    }

    public void removeAll(Collection<DataTable> cc) {
        for (DataTable table : cc) {
            removeTable(table);
        }
    }

    public void removeTable(DataTable table) {
        tableList.remove(tableList.indexOf(table));
    }

    public void clear() {
        tableList.clear();
    }

    public int size() {
        return tableList.size();
    }

    public boolean isEmpty() {
        return tableList.isEmpty();
    }

    @Override
    public Iterator<DataTable> iterator() {
        return tableList.iterator();
    }

}
