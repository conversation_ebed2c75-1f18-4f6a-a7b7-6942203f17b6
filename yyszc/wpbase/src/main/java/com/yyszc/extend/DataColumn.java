package com.yyszc.extend;

import java.sql.Types;
import java.util.Date;

public class DataColumn extends ExObject {
    private Object columnValue =null;
    private String columnName;
    private String captionName;
    private int dataType;

    public DataColumn() {
        this("default",Types.VARCHAR,new String(""));
    }

    public DataColumn(String columnName,int dataType, Object columnValue) {
        this.columnName = columnName;
        this.setDataType(dataType);
        this.columnValue = columnValue;
    }

    public String getColumnName() {
        return this.columnName;
    }

    public void setColumnName(String columnName) {
        this.columnName = columnName;
    }

    public Object getColumnValue() {
        Object retobj=this.columnValue;
        if(retobj==null)
        {
            retobj=new Object();
            switch (this.dataType) {
                case Types.ARRAY:
                case Types.CHAR:
                case Types.NCHAR:
                case Types.VARCHAR:
                case Types.NVARCHAR:
                case Types.CLOB:
                case Types.NCLOB:
                case Types.BLOB:
                case Types.ROWID:
                case Types.LONGVARCHAR:
                case Types.LONGNVARCHAR:
                case Types.LONGVARBINARY:
                case Types.NUMERIC:
                case Types.INTEGER:
                case Types.FLOAT:
                case Types.DECIMAL:
                case Types.TINYINT:
                case Types.DOUBLE:
                case Types.REAL:
                    retobj=new String("");
                    break;
                case Types.DATE:
                case Types.TIMESTAMP:
                case Types.TIME:
                    retobj=new Date();
                    break;
            }
        }
        return retobj;
    }

    public void setColumnValue(Object columnValue) {
        this.columnValue = columnValue;
    }

    public String getCaptionName() {
        return captionName;
    }

    public void setCaptionName(String captionName) {
        this.captionName = captionName;
    }

    public int getDataType() {
        return dataType;
    }

    public void setDataType(int dataType) {
        this.dataType = dataType;
    }

    @Override
    public String toString() {
        return this.columnName;
    }

}
