function szyyAjax(options) {
	$.ajax(options);
}

function szyyAjaxByCookie(options) {
	options["headers"]={
        "Authorization":getCookie("Admin-Token")
	};
    var successMethod  = options.success;
    options.success = function (data) {
        if(data.code==401) {
        }
        successMethod(data);
    }
    $.ajax(options);
}
function getCookie(c_name) {
  if (document.cookie.length > 0) {
    c_start = document.cookie.indexOf(c_name + "=");
    if (c_start != -1) {
      c_start = c_start + c_name.length + 1;
      c_end = document.cookie.indexOf(";", c_start);
      if (c_end == -1) c_end = document.cookie.length;
      return unescape(document.cookie.substring(c_start, c_end));
    }
  }
  return "";
}
function szyyAjaxNotCookie(options) {
    var successMethod  = options.success;
    options.success = function (data) {
        if(data.code==401) {
            var loginShowBox = "  <div style=\"position: absolute;top:0px;;width: 100%;height: 100%;background-color: rgba(0,0,0,1);display: none\" onclick=\"hideLoginShowBox1()\" id=\"loginShowPop1\">\n" +
                "        <div style=\"background: #000f1d ;position: absolute;z-index: 10000;height: 80%;width: 80%;margin-top:5%;left: 10%;box-shadow: 0 0 15px 10px #0ca0a8;top: 3.2vh!important;padding: 0!important;\">\n" +
                "            <div id=\"loginShowPop\" style=\"height: 100%;width: 100%;\"></div>\n" +
                "        </div>\n" +
                "    </div>";

            document.getElementsByTagName("body")[0].innerHTML+=loginShowBox;
            var pop = document.getElementById("loginShowPop");
            var pop1 = document.getElementById("loginShowPop1");
            var url = "/yydp/FBLH/alarm.html";
            pop.innerHTML = '<object type="text/html" data="' + url + '" width="100%" height="100%"></object>';
            pop1.style.display = 'block';
        }
        successMethod(data);
    }
    $.ajax(options);
}

function hideLoginShowBox() {
	var pop = document.getElementById("loginShowPop1");
	document.getElementsByTagName("body")[0].removeChild(pop);
	document.getElementsByClassName("")
}
