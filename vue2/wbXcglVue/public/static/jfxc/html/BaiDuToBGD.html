<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests"/>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <meta http-equiv="cache-control" content="no-cache">
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no" />
    <style type="text/css">
        body, html{width: 100%;height: 100%;margin:0;font-family:"微软雅黑";font-size:14px;}
        #l-map{height:95%;width:100%;}
        #r-result{width:100%;}
    </style>
    <script type="text/javascript" src="https://api.map.baidu.com/getscript?v=4.0&ak=1XjLLEhZhQNUzd93EjU5nOGQ&s=1"></script>
    <title>地图</title>
</head>
<body>
<div id="r-result">
    选择地址:<input type="text" onKeyUp="keyup(event)" id="address" size="20" value="百度" style="width:250px;" />
    GPS：<input id="localtion" disabled = "disabled"  style="width:250px;"/>
    <input type="button" onclick = "setGPS()" value="确定"/>
</div>
<div id="l-map"></div>

<div id="searchResultPanel" style="border:1px solid #C0C0C0;width:150px;height:auto; display:none;"></div>
</body>
</html>
<script type="text/javascript">
    // 百度地图API功能
    function G(id) {
        return document.getElementById(id);
    }

    var map = new BMap.Map("l-map", { enableMapClick: false });
    var redPoint;
    var g_point;
    var x_pi = 3.14159265358979324 * 3000.0 / 180.0;
    // var lng = 0;
    // var lat = 0;

    var point = new BMap.Point(121.5374851,29.8570321);
    //var point = new BMap.Point(lng, lat);

    map.centerAndZoom("宁波", 12);                 // 初始化地图,设置城市和地图级别。
    map.enableScrollWheelZoom(true);
    var ac = new BMap.Autocomplete(    //建立一个自动完成的对象
        {"input" : "address"
            ,"location" : map
        });
    // 创建地址解析器实例
    var myGeo = new BMap.Geocoder();
    // 将地址解析结果显示在地图上,并调整地图视野
   //getLocalByAddr();
    map.addEventListener("click", function (e) {
        map.clearOverlays();
        //alert(e.point.lng + "," + e.point.lat);
        writeLocalIntoInput(e.point);
        map.removeOverlay(redPoint);
        redPoint = new BMap.Marker(e.point);
        map.addOverlay(redPoint);
        g_point = e.point;
    });
    ac.addEventListener("onhighlight", function(e) {  //鼠标放在下拉列表上的事件
        var str = "";
        var _value = e.fromitem.value;
        var value = "";
        if (e.fromitem.index > -1) {
            value = _value.province +  _value.city +  _value.district +  _value.street +  _value.business;
        }
        str = "FromItem<br />index = " + e.fromitem.index + "<br />value = " + value;

        value = "";
        if (e.toitem.index > -1) {
            _value = e.toitem.value;
            value = _value.province +  _value.city +  _value.district +  _value.street +  _value.business;
        }
        str += "<br />ToItem<br />index = " + e.toitem.index + "<br />value = " + value;
        G("searchResultPanel").innerHTML = str;
    });

    var myValue;
    ac.addEventListener("onconfirm", function(e) {    //鼠标点击下拉列表后的事件
        var _value = e.item.value;
        myValue = _value.province +  _value.city +  _value.district +  _value.street +  _value.business;
        G("searchResultPanel").innerHTML ="onconfirm<br />index = " + e.item.index + "<br />myValue = " + myValue;

        setPlace();
        getLocalByAddr();

    });

    function bd09_To_Gcj02(lat,lon) {
        var x = lon - 0.0065, y = lat - 0.006;
        var z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_pi);
        var theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_pi);
        var tempLon = z * Math.cos(theta);
        var tempLat = z * Math.sin(theta);
        //tempLon = tempLon.toFixed(7);
        //tempLat = tempLat.toFixed(7);

        return tempLon+","+tempLat;
    }

    function bd_decrypt(bd_lng, bd_lat) {
        var X_PI = Math.PI * 3000.0 / 180.0;
        var x = bd_lng - 0.0065;
        var y = bd_lat - 0.006;
        var z = Math.sqrt((x * x) +( y * y)) - 0.00002 * Math.sin(y * X_PI);
        var theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * X_PI);
        var gg_lng = z * Math.cos(theta);
        gg_lng = gg_lng.toFixed(7)
        var gg_lat = z * Math.sin(theta);
        gg_lat = gg_lat.toFixed(7)
        return gg_lng+","+ gg_lat;
    }

    //将坐标点写到框中
    function writeLocalIntoInput(p) {
        if (p) {
            var gps = bd_decrypt(p.lng,p.lat);
            document.getElementById("localtion").value = gps;
            //document.getElementById("localtion").value = p.lng + ',' + p.lat;

        } else {
            document.getElementById("localtion").value = '';
        }
    }

    //根据地址获取坐标点信息，并将坐标点作为中心点
    function getLocalByAddr() {
        var address = document.getElementById("address").value;
        // console.log(address);
        myGeo.getPoint(address, function (point) {
            writeLocalIntoInput(point);
            if (point) {
                map.centerAndZoom(point, 16);
                map.removeOverlay(redPoint);
                redPoint = new BMap.Marker(point);
                map.addOverlay(redPoint);
                g_point = point;
                //var infoWindow = new BMap.InfoWindow(point.lat+','+point.lng, opts);  // 创建信息窗口对象
                //map.openInfoWindow(infoWindow,point); //开启信息窗口
            } else {
                alert("您选择地址没有解析到结果!");
            }
        }, "北京市");
    }
    function setPlace(){
        map.clearOverlays();    //清除地图上所有覆盖物
        function myFun(){
            var pp = local.getResults().getPoi(0).point;    //获取第一个智能搜索的结果
            map.centerAndZoom(pp, 18);
            map.addOverlay(new BMap.Marker(pp));    //添加标注
        }
        var local = new BMap.LocalSearch(map, { //智能搜索
            onSearchComplete: myFun
        });
        local.search(myValue);
    }
    var opts = {
            width: 400,     // 信息窗口宽度
            height: 60,     // 信息窗口高度
            title: "", // 信息窗口标题
            enableMessage: true, //设置允许信息窗发送短息
            message: ""
        }

        /
        //调用gpsspg网站，jsonp形式，确定，每日每个oid和key只有访问2000次

        /*参数lat_lng 可以传递 lat或者lng中的一个*/
        function baiduToGPS(lat_lng) {
            var abs_lat_lng = Math.abs(lat_lng);
            //a_  意为all，含有整数和小数部分。
            //o_  意为only，只有小数部分
            var du = parseInt(abs_lat_lng); //度(不含小数部分)
            var o_du = lat_lng - du;   //度(只有小数部分)
            var a_fen = o_du * 60;  //分(含有整数和小数部分)
            var fen = parseInt(a_fen);  //分(不含小数部分)
            var o_fen = a_fen - fen;  //分(只有小数部分)
            var a_miao = o_fen * 60;   //秒(含有整数和小数部分)
            var miao = a_miao.toFixed(2);      //秒(取两位小数)
            var value = du + '°' + fen + '′' + miao + '″';
            // console.log(value);
            return value;
        }
    function keyup(event) {
        if (event.keyCode == "13") {
            //回车执行查询
            getLocalByAddr();
        }
    }

    function setGPS() {
        var gps = document.getElementById("localtion").value;
        window.parent.document.getElementById("clbgd.gps").value = gps;
        var win = window.parent.document.getElementById('clbgd.dtDialogClose');
        if (win) { win.click() }
    }
</script>
