<template>
    <splitpanes @resized="handleLeftResize($event)" vertical>
        <pane min-size="20" :size="leftResize" class="box-pane">
            <slot name="part1"/>
        </pane>
        <pane min-size="30" :size="100-leftResize">
            <splitpanes @resized="handleRightResize($event)" horizontal>
                <pane min-size="30" :size="topResize" class="box-pane" >
                    <slot name="part2"/>
                </pane>
                <pane min-size="20" :size="100-topResize">
                    <splitpanes 
                      vertical
                      @resized="handleRightBottomResize($event)">
                        <pane min-size="20" :size="bottomResize" class="box-pane">
                            <slot name="part3"/>
                        </pane>
                        <pane min-size="20" :size="100-bottomResize" class="box-pane">
                            <slot name="part4"/>
                        </pane>
                    </splitpanes>
                </pane>
            </splitpanes>
        </pane>
    </splitpanes>
</template>

<script>
import {Splitpanes, Pane} from 'splitpanes'
import { mapState } from 'vuex'
import 'splitpanes/dist/splitpanes.css'

export default {
    components:{Splitpanes, Pane},
    data(){
      return {
        resizePercent:{}
      }
    },
    computed:{
      ...mapState({
        leftResize: state=> state.settings.leftResize,
        topResize: state=> state.settings.topResize,
        bottomResize: state=> state.settings.bottomResize,
      })
    },
    methods:{
      handleLeftResize(event){
        const value = event[0].size
        this.$store.dispatch('settings/setResize',{key: 'leftResize',value})
        this.savePercent(value,this.topResize,this.bottomResize)
        this.$emit('resized')
      },
      handleRightResize(event){
        const value = event[0].size
        this.$store.dispatch('settings/setResize',{key: 'topResize',value})
        this.savePercent(this.leftResize,value,this.bottomResize)
        this.$emit('resized')
      },
      handleRightBottomResize(event){
        const value = event[0].size
        this.$store.dispatch('settings/setResize',{key: 'bottomResize',value})
        this.savePercent(this.leftResize,this.topResize,value)
        this.$emit('resized')
      },
      //localStorage保存百分比
      savePercent(leftResize,topResize,bottomResize){
        this.resizePercent = {leftResize,topResize,bottomResize}
        window.localStorage.setItem('resize-percent',JSON.stringify(this.resizePercent))
      },
    }
}
</script>

<style lang="scss" scoped>
.splitpanes__pane {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff !important;
  border-radius:10px;
  
}

.box-pane{
  box-shadow: 0 0 6px #d9e2f1aa;
}

</style>