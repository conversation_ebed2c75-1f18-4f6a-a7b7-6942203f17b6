<template>
  <el-table
      ref="tableRef"
      :data="tableData"
      stripe
      border
      highlight-current-row
      @current-change="handleCurrentChange"
      @row-dblclick="rowDbClick"
      :cell-style="cellStyle"
      :cell-class-name="tableCellClassName"
      :row-class-name="tableRowClassName"
      style="width: 100%"
      height="100%"
      v-loading="loading"
      v-watermark="{label:watermark}">
    <el-table-column v-if="needIndex" type="index" label="序号" width="50" align="center">
      <template slot-scope="scope">
        {{ (queryParam.pageNum - 1) * queryParam.pageSize + 1 + scope.$index }}
      </template>
    </el-table-column>
    <template v-for="(item, index) in tableOptions">
      <el-table-column
          :key="index"
          :index="index"
          :prop="item.prop"
          :label="item.label"
          :align="item.align || 'center'"
          :width="item.width || ''"
          :show-overflow-tooltip="item.tooltip || false"
          :sortable="item.sortable === false ? false : true">
        <template slot-scope="scope">
          <slot
              v-if="item.slot"
              :name="scope.column.property"
              :row="scope.row"
              :column="scope.column"
              :rowIndex="scope.$index">
          </slot>
          <span v-else>{{ scope.row[scope.column.property] }}</span>
        </template>
      </el-table-column>
    </template>
  </el-table>
</template>
<script>
export default {
  name: 'index',
  props: {
    needIndex: {//序号显示
      type: Boolean,
      default: true
    },
    tableData: {
      type: Array,
      default: () => []
    },
    tableOptions: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    queryParam: {
      type: Object,
      default: () => {
        return {
          pageNum: 1,
          pageSize: 10
        }
      }
    },
    // tableCellClassName: { //给单元格绑定横向和竖向的index，这样就能确定是哪一个单元格
    //   type: Function,
    //   default: function () {}
    // },
    cellStyleVal: 0,  //样式设置
  },
  computed: {
    watermark() {
      return this.$store.state.user.watermark
    }
  },
  methods: {
    //高亮
    setCurrent(row) {
      this.$refs.tableRef.setCurrentRow(row)
    },
    //选中数据
    handleCurrentChange(val) {
      this.$emit('getCurrentData', val)
    },
    //双击事件
    rowDbClick(row, column){
      this.$emit('rowDblClick',row, column);
    },

    // 把每一列的索引放进column
    tableCellClassName({ column, columnIndex }) {
      column.index = columnIndex;
    },
    // 把每一行的索引放进row
    tableRowClassName({ row, rowIndex }) {
      row.index = rowIndex;
    },

    /** 设置指定行、列、具体单元格颜色 */
    cellStyle({row, column, rowIndex, columnIndex}) {
      //每日考勤情况样式设置
      if (this.cellStyleVal == 1) {
        if (row.type.includes('0.9')) {
          return ''
        } else if (row.type == (',')) {
          return ''
        } else {
          return 'color: red'
        }
      } else if (this.cellStyleVal == 2){
        //传2的时候就是行程汇总
        if (row.isTab == 1){
          return 'backgroundColor:#ff949c'
        }else if(row.isSpecialItinerary == 1){
          return 'backgroundColor:#ff99cc'
        }else {
          return  ''
        }
      }
      else {
        return ''
      }

    },


  }
}
</script>
<style lang="scss" scoped>

</style>
