<template>
    <div>
        <logo :collapse="false" />
        <el-scrollbar wrap-class="scrollbar-wrapper">
            <el-menu
                :default-active="$route.path"
                router
                :unique-opened="true"
                class="el-menu-vertical-demo"
                :active-text-color="theme">
                <component
                    v-for="route in menuList"
                    :key="route.url"
                    :index="route.text"
                    :is="(route.children && route.children.length > 0) ? 'el-submenu' : 'el-menu-item'">
                    <template slot="title">
                        <i class="iconfont font-size14" style="margin-right:10px">&#xe790;</i>
                        <span slot="title" class="font-size14">{{route.text}}</span>
                    </template>
                    <template v-if="route.children && route.children.length > 0">
                        <el-menu-item
                            v-for="routeChild in route.children"
                            :key="routeChild.url"
                            :index="routeChild.url"
                            :disabled="!routeChild.hasPermi"
                            :route="{path: routeChild.url}">
                            <span slot="title">{{routeChild.text}}</span>
                        </el-menu-item>
                    </template>
                </component>
            </el-menu>
        </el-scrollbar>
    </div>
</template>

<script>
import Logo from "./Logo";
import {getModule} from "../../../api/login";

export default {
    components: { Logo},
    data(){
        return {
            menuList:[
                {
                    text: '行程管理',
                  children:[
                    {"id":"JDWKQ01BM01","text":"组织架构","url":"/dfdwJfxc/zzjg","Parameter":0,"leaf":true,"DisplayName":"",
                    hasPermi: (this.$store.getters.roles.indexOf('协同办公-行程管理-组织架构') > -1 )
                    },
					          {"id":"JDWKQ01BM01","text":"统计查询","url":"/dfdwJfxc/tjcx","Parameter":0,"leaf":true,"DisplayName":""
                     ,hasPermi: (this.$store.getters.roles.indexOf('协同办公-行程管理-统计查询') > -1 || this.$store.getters.roles.indexOf('协同办公-行程管理-监理站站长') > -1)
                    },
                    {"id":"JDWKQ01GR01","text":"人员管理","url":"/dfdwJfxc/rygl","Parameter":0,"leaf":true,"DisplayName":""
                     ,hasPermi: (this.$store.getters.roles.indexOf('协同办公-行程管理-人员管理') > -1 )
                    },
                    {"id":"JDWKQ01BM01","text":"行程汇总","url":"/dfdwJfxc/xchz","Parameter":0,"leaf":true,"DisplayName":""
                      ,hasPermi: (this.$store.getters.roles.indexOf('协同办公-行程管理-行程汇总') > -1 || this.$store.getters.roles.indexOf('协同办公-行程管理-监理站站长') > -1)
                    },
                    // {"id":"JDWKQ01GR01","text":"行程报备","url":"/dfdwJfxc/xcbb","Parameter":0,"leaf":true,"DisplayName":""
                    //   ,hasPermi: (this.$store.getters.roles.indexOf('协同办公-行程管理-计划上报') > -1 || this.$store.getters.roles.indexOf('协同办公-行程管理-计划审批') > -1)
                    // },
                    {"id":"JDWKQ01GR01","text":"行程计划","url":"/dfdwJfxc/xcjh","Parameter":0,"leaf":true,"DisplayName":""
                      ,hasPermi: (this.$store.getters.roles.indexOf('协同办公-行程管理-计划上报') > -1 || this.$store.getters.roles.indexOf('协同办公-行程管理-监理站站长') > -1)
                    },
                    {"id":"JDWKQ01GR01","text":"打卡照片汇总","url":"/dfdwJfxc/xcdkzphz","Parameter":0,"leaf":true,"DisplayName":""
                      ,hasPermi: (this.$store.getters.roles.indexOf('协同办公-行程管理-行程汇总') > -1 || this.$store.getters.roles.indexOf('协同办公-行程管理-监理站站长') > -1)
                    }
                    ]
                },
            ]
        }
    },
    computed: {
        theme(){
            return this.$store.state.settings.theme
        }
    },
    created(){
      getModule().then((res) => {
        //TODO 获取后台模块列表 默认跳转到第一个
        if(this.$router.currentRoute.fullPath !== this.menuList[0].children[0].url)
          this.$router.push(this.menuList[0].children[0].url)
      })
    }
};
</script>
