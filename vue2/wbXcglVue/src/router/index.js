import Vue from 'vue'
import VueRouter from 'vue-router'
import Layout from '@/layout'

Vue.use(VueRouter)

export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect')
      }
    ]
  },
  {
    path: '/404',
    component: () => import('@/views/error/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true
  },
  {
    path: "*",
    name: "NotFound",
    component: () => import("@/views/error/404"),
  },
  //协同办公路由
  {
    path: '/dfdwJfxc',
    component: Layout,
    // redirect: 'index',
    children:[
      {
        path: 'index.html',
        name: 'index.html',
        component: () => import('@/views/index'),
      },
      {
        path: 'zzjg',
        name: 'zzjg',
        component: () => import('@/views/jfxc/zzjg/index'),
      },
      {
        path: 'rygl',
        name: 'rygl',
        component: () => import('@/views/jfxc/rygl/index'),
      },
      {
        path: 'gcgl',
        name: 'gcgl',
        component: () => import('@/views/jfxc/gcgl/index'),
      },
      {
        path: 'tjcx',
        name: 'tjcx',
        component: () => import('@/views/jfxc/tjcx/index'),
      },
      {
        path: 'xchz',
        name: 'xchz',
        component: () => import('@/views/jfxc/xchz/index'),
      },
      // {
      //   path: 'xcbb',
      //   name: 'xcbb',
      //   component: () => import('@/views/jfxc/xcbb/index'),
      // },
      {
        path: 'xcjh',
        name: 'xcjh',
        component: () => import('@/views/jfxc/xcjh/index'),
      },
      {
        path: 'xcdkzphz',
        name: 'xcdkzphz',
        component: () => import('@/views/jfxc/xcdkzphz/index'),
      },
    ]
  }
]

const router = new VueRouter({
  mode: 'history',
  scrollBehavior: () => ({ y: 0 }),
  base: process.env.NODE_ENV === 'development' ? '/' : process.env.VUE_APP_BASE_API,
  routes:constantRoutes
})

export default router
