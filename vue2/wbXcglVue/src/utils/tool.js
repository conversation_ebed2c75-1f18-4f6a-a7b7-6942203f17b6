function isEmpty (obj) {
  if ((typeof obj === 'string')) {
    return !obj || obj.replace(/\s+/g, '') === ''
  } else {
    return (!obj || JSON.stringify(obj) === '{}' || obj.length === 0)
  }
}

function isNotEmpty (obj) {
  return !isEmpty(obj)
}

function copy (obj) {
  if (isNotEmpty(obj)) {
    return JSON.parse(JSON.stringify(obj))
  }
}

function array2Tree (array, parentId) {
  if (isEmpty(array)) {
    return []
  }
  const result = []
  for (let i = 0; i < array.length; i++) {
    const c = array[i]
    // console.log(Number(c.parent), Number(parentId));
    if (Number(c.pid) === Number(parentId)) {
      result.push(c)
      // 递归查看当前节点对应的子节点
      const children = array2Tree(array, c.id)
      if (isNotEmpty(children)) {
        c.children = children
      }
    }
  }
  return result
}

function formatMonth (time) {
  let date = new Date()
  if (time !== undefined) {
    date = new Date(time)
  }
  let timeStr = date.getFullYear() + '-'
  const Month = date.getMonth() + 1
  if (Month < 10) {
    timeStr += '0'
  }
  timeStr += date.getMonth() + 1
  timeStr += '-01'
  return timeStr
}

//通过本月第一天获取本月最后一天
function getMonthLast(time){
  let date = new Date()
  if (time !== undefined) {
    date = new Date(time)
  }
  //计算时间差，就是当前月的最后一天
  return getDate(new Date(date.getFullYear(),date.getMonth() + 1,0));
}

function dateFormat (time) {
  let date = new Date()
  if (time !== undefined) {
    date = new Date(time)
  }
  const year = date.getFullYear()
  const month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
  const day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
  // var hours = date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
  // var minutes = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
  // var seconds = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
  return year + '-' + month + '-' + day + ' 00:00:00'
}

function getDate (time) {
  let date = new Date()
  if (time !== undefined) {
    date = new Date(time)
  }
  const year = date.getFullYear()
  const month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
  const day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
  // var hours = date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
  // var minutes = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
  // var seconds = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
  return year + '-' + month + '-' + day
}

// file转base64
function getBase64 (file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    let fileResult = ''
    // 开始转
    reader.readAsDataURL(file)
    reader.onload = function () {
      fileResult = reader.result
    }
    // 转 失败
    reader.onerror = function (error) {
      reject(error)
    }
    // 转 结束  咱就 resolve 出去
    reader.onloadend = function () {
      resolve(fileResult)
    }
  })
}

function downLoad (blob, fileName) {
  // 导出文件名 // 对于<a>标签，只有 Firefox和Chrome（内核）支持 download 属性
  // IE10以上支持blob但是依然不支持 download
  if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
    const link = document.createElement('a') // 创建a标签
    link.download = fileName // a标签添加属性
    link.style.display = 'none'
    link.href = URL.createObjectURL(blob)
    document.body.appendChild(link)
    link.click() // 执行下载
    URL.revokeObjectURL(link.href) // 释放url
    document.body.removeChild(link) // 释放标签
  } else { // 其他浏览器
    navigator.msSaveBlob(blob, fileName)
  }
}

function getFileNameTime(){
  let time = new Date()
  let year = time.getFullYear().toString()
  let month =(time.getMonth()+1)<10?'0'+(time.getMonth()+1) : (time.getMonth()+1) +''
  let day =time.getDate()<10?'0'+(time.getDate()) : (time.getDate()) +''
  let hours =time.getHours()<10?'0'+(time.getHours()) : (time.getHours()) +''
  let minutes =time.getMinutes()<10?'0'+(time.getMinutes()) : (time.getMinutes()) +''
  let seconds =time.getSeconds()<10?'0'+(time.getSeconds()) : (time.getSeconds()) +''
  return year + month + day + hours + minutes + seconds
}

export {
  isEmpty,
  isNotEmpty,
  copy,
  array2Tree,
  formatMonth,
  dateFormat,
  getBase64,
  downLoad,
  getDate,
  getMonthLast,
  getFileNameTime

}
