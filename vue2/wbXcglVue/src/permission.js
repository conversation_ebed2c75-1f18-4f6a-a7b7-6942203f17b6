import router from './router'
import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { getToken } from '@/utils/auth'
// import { isRelogin } from '@/utils/request'

NProgress.configure({ showSpinner: false })

const whiteList = ['/kqgl','/kqgl/yyglAdd','/kqgl/yyglProcess','/kqgl/yyglSearch','/kqgl/yytjSearch', '/auth-redirect','/positionSetting','/positionSetting/add'
,'/jfxc/zzjg']
// const whiteList = ['/login', '/auth-redirect', '/bind', '/register']

router.beforeEach((to, from, next) => {
  console.log(to)

  NProgress.start()
  if (getToken()) {
    // to.meta.title && store.dispatch('settings/setTitle', to.meta.title)
    /* has token*/
    if (to.path === '/login') {
      next({ path: '/' })
      NProgress.done()
    } else {
      if (store.getters.roles.length === 0) {
        // isRelogin.show = true
        // 判断当前用户是否已拉取完user_info信息
        store.dispatch('GetInfo').then(()=>{
          // isRelogin.show = false
          // store.dispatch('GenerateRoutes').then(accessRoutes => {
          //     // 根据roles权限生成可访问的路由表
          //     router.addRoutes(accessRoutes) // 动态添加可访问路由表
          //   next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
          // })
          next()
          NProgress.done()
        }).catch((err)=>{
          store.dispatch('LogOut').then(()=>{
            Message.error(err);
            next({ path: '/' })
          })
        })
      } else {
        switch (to.path){
          case '/jfxc/xchz':
            if (store.getters.permissions.includes('JDWXC01HZ01QX01')){
              next()
            }else {
              next({path: '/401'})
            }
              break
          case '/jfxc/rygl':
            if (store.getters.permissions.includes('JDWXC01RY01QX01')){
              next()
            }else {
              next({path: '/401'})
            }
            break
          // case '/jfxc/xcbb':
          //   if (store.getters.permissions.includes('91310101')){
          //     next()
          //   }else {
          //     next({path: '/401'})
          //   }
          //   break

			case '/jfxc/zzjg':
			if (store.getters.permissions.includes('JDWXC01ZZ01QX01')){
			  next()
			}else {
			  next({path: '/401'})
			}
			break
            case '/jfxc/xcjh':
                if (store.getters.permissions.includes('JDWXC01JH01QX01')){
                    next()
                }else {
                    next({path: '/401'})
                }
                break

            case '/jfxc/tjcx':
			if (store.getters.permissions.includes('JDWXC01TJ01QX01')){
			  next()
			}else {
			  next({path: '/401'})
			}
			break


          default:
            next()
                break
        }
        // next()
      }
    }
  } else {
    // 没有token
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next()
    } else {
      next(`/login?redirect=${to.fullPath}`) // 否则全部重定向到登录页
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  NProgress.done()
})
