<template>

  <div class="main" ref="main">


    <div class="list-box" style="width: 20%">
      <div class="operate-pannel">
        <div v-loading="treeLoading">
          <el-tree :data="data"
                   ref="tree"
                   :props="defaultProps"
                   node-key="id"
                   @node-click="handleNodeClick"
                   :expand-on-click-node="false"
                   :default-expanded-keys="[1]"
                   :highlight-current="true"
          />
        </div>

      </div>
    </div>
    <div class="process-info" style="width: 79%;height: 100%">
      <div class="search-container">
        <div class="operate-pannel">
          <div class="search-box">
            <span class="font-size14">日期：</span>
            <el-date-picker
                v-model="queryParams.time"
                :clearable="false"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                @change="handleChange"
                style="width: 40%">
            </el-date-picker>
            <el-button
                v-if="this.$store.getters.permissions.includes('JDWXC01HZ01QX01')"
                size="mini"
                type="text"
                icon="el-icon-search"
                @click="getXcGl()"
            >查询
            </el-button>
            <el-button
                v-if="this.$store.getters.permissions.includes('JDWXC01HZ01QX01')"
                class="rygf"
                size="mini"
                type="text"
                icon="el-icon-refresh"
                @click="resetQueryParam()"
            >重置
            </el-button>
            <el-button
                v-if="this.$store.getters.permissions.includes('JDWXC01HZ01QX02')"
                size="mini"
                type="text"
                class="rygf"
                icon="el-icon-download"
                @click="exportExcel()"
            >导出
            </el-button>
            <el-button
                v-if="this.$store.getters.permissions.includes('JDWXC01HZ01QX03')"
                size="mini"
                type="text"
                class="rygf"
                icon="el-icon-view"
                @click="openInfo()"
            >详情
            </el-button>
            <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>

          </div>
        </div>
        <div class="table-box">
          <Table
              :tableData="xchzList"
              :tableOptions="realTableOptions"
              :loading="xcglLoading"
              @getCurrentData="select"
              :show-overflow-tooltip="true"
              :cell-style-val="2"
              height="250px"
          >
            <template slot-scope="scope">
              {{
                (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
              }}
            </template>
            <template slot="approvingState" slot-scope="scope">
              <span v-if="scope.row.approvingState === 0 ">未提交</span>
              <span v-if="scope.row.approvingState === 1 ">审核中</span>
              <span v-if="scope.row.approvingState === 2 ">已驳回</span>
              <span v-if="scope.row.approvingState === 3 ">已审核</span>
              <span v-if="scope.row.approvingState === 4 ">已过期</span>
              <span v-if="scope.row.approvingState === 5 ">已作废</span>
            </template>
            <template slot="notarizeState" scope="scope">
              <span v-if="scope.row.notarizeState === 0 ">未提交</span>
              <span v-if="scope.row.notarizeState === 1 ">审批中</span>
              <span v-if="scope.row.notarizeState === 2 ">已驳回</span>
              <span v-if="scope.row.notarizeState === 3 ">确认通过</span>
            </template>
          </Table>
          <Pagination
              @handleRefresh="handleCurrentChange"
              :queryParam="queryParams"
              layout="total, prev, pager, next, jumper"
              :total="queryParams.total"
          />
        </div>
      </div>

      <el-dialog title="详情" :visible.sync="dialogTableVisible" class="abow_dialog">
        <XchzDetail ref="xchzDetailDialog" :lc-id="clbgParams.lcId" @handleClose="dialogTableVisible = false"/>
      </el-dialog>


    </div>

  </div>

</template>
<script>
import {mapActions} from 'vuex'
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'
import {getXchz, getDeptList, exportExcel} from '@/api/jfxc/xchz'
import SplitPane from "@/components/split-pane";
import {downLoad, getFileNameTime} from "@/utils/tool";
// import XchzDetail from "views/xcgl/xchz/components/detail.vue";
import XchzDetail from "views/jfxc/xcjh/components/detail.vue";

export default {
  name: 'index',
  components: {XchzDetail, SplitPane, Table, Pagination, Dropdown},
  data() {
    return {
      data: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      flag: false,
      // 拷贝初始化查询参数
      deptId: null,
      clbgParams:{},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        time: [],
        startTime: null,
        endTime: null,
      },
      realTableOptions: [],
      tableOptions: [
        {label: '部门', prop: 'deptName',tooltip:true},
        {label: '姓名', prop: 'userName',tooltip:true},
        {label: '出行日期', prop: 'tripDate',tooltip:true},
        {label: '出行事由', prop: 'note',tooltip:true},
        {label: '里程', prop: 'performMileage',tooltip:true},
        {label: '费用', prop: 'performCost',tooltip:true},
        {label: '起点', prop: 'startSite',tooltip:true},
        {label: '起点时间', prop: 'startTime',tooltip:true},
        {label: '终点', prop: 'endSite',tooltip:true},
        {label: '终点时间', prop: 'endTime',tooltip:true},
        {label: '途经点', prop: 'middleSite',tooltip:true},
        {label: '实际到达地gps定位地名称', prop: 'arrivedLocationName',tooltip:true},
        {label: '审批人', prop: 'approveUserName',tooltip:true},
        {label: '确认状态', prop: 'notarizeState',tooltip:true,slot: true},
        {label: '审批状态', prop: 'approvingState',tooltip:true,slot: true},
        {label: '申请延期天数', prop: 'applyPostponeDays',tooltip:true},
        {label: '审批延期天数', prop: 'approvePostponeDays',tooltip:true},
      ],
      xcglLoading: false,
      treeLoading:true,
      xchzList: [],
      dialogTableVisible: false,
    };
  },
  methods: {
    ...mapActions(['GetInfo']),
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },
    /** queryParam事件 */
    handleChange() {
      //this.queryParams.deptId = 1
      this.queryParams.pageNum = 1
      this.getXcGl()
    },
    /** 分页查询 */
    handleCurrentChange(val) {
      this.queryParams = val
      this.getXcGl()
    },
    getXcGl() {
      if (this.queryParams.time == null) {
        this.queryParams.startTime = null
        this.queryParams.endTime = null
      } else {
        this.queryParams.startTime = this.queryParams.time[0]
        this.queryParams.endTime = this.queryParams.time[1]
      }
      this.xcglLoading = true
      getXchz(this.queryParams).then((res) => {
        if (res.success == true){
          this.xchzList = res.result.records
          this.queryParams.total = res.result.total
        }else {
          this.$message({
            showClose: true,
            message: res.message,
            type: 'error',
            offset:400
          });
        }
      }).finally(() => {
        this.xcglLoading = false
      })
    },
    /**  查看详情  **/
    openInfo(){
      if (this.clbgParams == null || this.clbgParams == undefined || this.clbgParams == ''){
        this.$message({
          showClose: true,
          message: '请选择一条数据',
          type: 'error',
          offset:400
        });
        return
      }

      this.dialogTableVisible = true;
      this.$refs.xchzDetailDialog.resetQuery();

      console.log("选中===")
      console.log( this.clbgParams)
    },

    /** 导出excel **/
    exportExcel(){

      if (this.queryParams.deptId == undefined || this.queryParams.deptId == null || this.queryParams.deptId ==''){
        this.$message({
          showClose: true,
          message: "请先点击选择左侧部门!",
          type: 'error',
          offset:400
        });
        return
      }
      this.xcglLoading = true
      exportExcel(this.queryParams).then((res)=>{
          let fileName ='行程汇总' + getFileNameTime() + '.xlsx'
          downLoad(res, fileName)
      }).finally(() => {
        this.xcglLoading = false
      })
    },

    //部门点击
    handleNodeClick(data, checked) {
      if (checked) {
        this.$refs.tree.setCheckedNodes([data])
        this.deptId = this.$refs.tree.getCheckedKeys()[0];
        // this.queryParams.deptId = this.$refs.tree.getCheckedKeys()[0];
        this.queryParams.deptId = data.id;
        this.deptId = data.id;
        this.queryParams.pageNum = 1;
        this.queryParams.pageSize = 10
        this.getXcGl();
      }
    },

    /** 单击表事件 */
    select (row) {
      this.selectID = row.lcId
      this.clbgParams = row
    },
    resetQueryParam(){
      this.queryParams = {
                            pageNum: 1,
                            pageSize: 10,
                            total: 0,
                            time: this.queryParams.time,
                            startTime: this.queryParams.startTime,
                            endTime: this.queryParams.endTime,
                            deptId:this.deptId
                          }

      this.getXcGl()
    },

    getDeptList(){
      getDeptList().then((res) => {
        let a = [];
        a.push(res.result)
        this.data = a
      }).finally(() => {
        this.treeLoading = false
      })
    },
    setQueryDate(){
      const now = this.getDateNow()
      this.queryParams.time = [now,now]
      this.queryParams.startTime = now
      this.queryParams.endTime = now
    },
    getDateNow(){
      let now = new Date()
      let year = now.getFullYear()
      let month = now.getMonth() + 1
      let day = now.getDate()
      return year + '-' + month + '-' + day
    }

  },

  created() {
    this.setQueryDate()
    this.getDeptList()
  },
  computed: {
    watermark() {
      return this.$store.state.user.watermark
    }
  },
};
</script>
<style lang="scss" scoped>

.main {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: space-between;
}


.list-box {
  height: 100%;
  box-shadow: 0 0 6px #d9e2f1aa;
  border-radius: 10px;
  padding: 10px;
  display: flex;
  flex-direction: column;
  transition: all .5s;

  .operate-pannel {
    height: 100%;
    width: 100%;

    justify-content: space-between;
    align-items: center;
    overflow: auto;
    background: #FFFFFF;
  }

}

.process-info {
  height: 100%;
  box-shadow: 0 0 6px #d9e2f1aa;
  border-radius: 10px;
  padding: 10px;
  transition: all .5s;
}

.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 100px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}
.abow_dialog {
  display: flex;
  justify-content: center;
  align-items: Center;
  overflow: hidden;
  .el-dialog {
    margin: 0 auto !important;
    height: 90%;
    overflow: hidden;
    .el-dialog__body {
      position: absolute;
      left: 0;
      top: 54px;
      bottom: 0;
      right: 0;
      padding: 0;
      z-index: 1;
      overflow: hidden;
      overflow-y: auto;
    }
  }
}

</style>
