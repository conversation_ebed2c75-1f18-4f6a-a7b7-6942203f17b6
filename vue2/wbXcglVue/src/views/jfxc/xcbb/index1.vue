<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">管理部门：</span>
        <el-select v-model="queryParams.groupId" filterable clearable placeholder="请选择管理部门" @change="handleChange">
          <el-option
              v-for="item in groupItem"
              :key="item.id"
              :label="item.groupname"
              :value="item.id">
          </el-option>
        </el-select>
        <span class="font-size14">告警类型：</span>
        <el-select v-model="queryParams.type" placeholder="请选择告警类型" @change="handleChange">
          <el-option
              v-for="item in gjType"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>
        <span class="font-size14">车牌：</span>
        <el-input v-model="queryParams.licensePlate" clearable placeholder="请输入车牌"
                  @change="handleChange"></el-input>

        <span class="font-size14">日期：</span>
        <el-date-picker
            v-model="queryParams.time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            @change="handleChange"
            style="width: 20%">
        </el-date-picker>
        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-search"
            @click="getCBJLList"
        >查询
        </el-button>

        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="resetQuery"
        >重置
        </el-button>
        <el-button
            size="mini"
            type="text"
            class="rygf"
            icon="el-icon-download"
            @click="openDialog('download')"
        >导出
        </el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>

      </div>
    </div>
    <div class="table-box">
      <Table
          :tableData="List"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          @getCurrentData="select"
          @row-dblclick="openDialog('edit')"
      >
        <template  slot-scope="scope">
          {{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}
        </template>

        <template slot="personType" slot-scope="scope">
          <div v-if="scope.row.personType == 1">
            车辆管理人员
          </div>
          <div v-if="scope.row.personType == 2">
            车辆调度人员
          </div>
          <div v-if="scope.row.personType == 3">
            车辆运监人员
          </div>
        </template>
        <template slot="type" slot-scope="scope">
          <div v-if="scope.row.type == 1">
            超速
          </div>
          <div v-if="scope.row.type == 2">
            被拔
          </div>
          <div v-if="scope.row.type == 3">
            超时
          </div>
          <div v-if="scope.row.type == 4">
            设备异常
          </div>
        </template>

      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>
  </div>
</template>
<script>
import { mapActions } from 'vuex'
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'
import {DownLoad} from "api/jfxc/xcbb";
import {downLoad} from "@/utils/tool";

export default {
  name: 'xcbb',
  components: {Table, Pagination, Dropdown},

  created () {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  computed: {
    watermark(){
      return this.$store.state.user.watermark
    }
  },
  mounted () {
    this.getCBJLList()
    GetGroupList({
      isShowAll: 1,
      parentId: 1
    }).then((res) => {
      this.groupItem = res.result
    })
  },
  data () {
    return {
      // 初始打开的折叠面板
      activeNames: ['1'],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        licensePlate: '',
        groupId: -1,
        time: [],
        StartTime: null,
        EndTime: null,
        type: 0
      },
      gjType: [
        {
          value: 0,
          label: '全部'
        }, {
          value: 1,
          label: '超速'
        }, {
          value: 2,
          label: '被拔'
        }, {
          value: 3,
          label: '超时'
        }, {
          value: 4,
          label: '设备异常'
        }
      ],
      // 查询部门列表
      groupItem: [],
      // 车队长列表
      List: [],
      // 遮罩层
      loading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 选择列表id
      selectID: 0,
      // 选择列表数据
      cbjlParams: {},
      dialogType: '',
      dialogTitle: '',
      cbjlDialog: false,
      realTableOptions:[],
      tableOptions: [
        {label: '车型', prop: 'carMold'},
        {label: '车牌', prop: 'licensePlate'},
        {label: '部门', prop: 'groupName'},
        {label: '开始时间', prop: 'StartTime'},
        {label: '结束时间', prop: 'EndTime'},
        {label: '人员类型', prop:'personType',slot: true},
        {label: '告警类型', prop:'type',slot: true}
      ],
    }
  },
  methods: {
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },
    /** queryParam事件 */
    handleChange () {
      this.queryParams.pageNum = 1
      this.getCBJLList()
    },
    /** 查询插拔记录 */
    getCBJLList () {
      this.loading = true
      if (this.queryParams.time == null) {
        this.queryParams.StartTime = null
        this.queryParams.EndTime = null
      } else {
        this.queryParams.StartTime = this.queryParams.time[0]
        this.queryParams.EndTime = this.queryParams.time[1]
      }
      GetCbjlList(this.queryParams).then((res) => {
        this.List = res.result.records
        this.queryParams.total = res.result.total
      }).finally(() => {
        this.loading = false
      })
    },
    /** 分页查询 */
    handleCurrentChange (val) {
      // debugger
      this.queryParams = val
      this.getCBJLList()
    },
    /** 重置查询 */
    resetQuery () {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.getCBJLList()
    },
    /** 单击表事件 */
    select (row) {
      this.selectID = row.id
      this.cbjlParams = row
    },
    /** 打开新增弹窗 */
    openDialog (type) {
      switch (type) {
        case 'download':
          this.groupItem.forEach(value => {
            if (value.id === this.queryParams.groupId) {
              this.queryParams.groupName = value.groupname
            }
          })
          DownLoad(this.queryParams).then((res) => {
            downLoad(res, 'GPS异常记录.xlsx')

          })
          break
      }
    },
    /** 关闭弹窗 */
    closeDialog () {
      this.cbjlDialog = false
    },
    /** 提交新增 */
    submitCdzgl (type) {
      switch (type) {
      }
    }
  }
}
</script>
<style lang="less" scoped>
.search-container {
  width: 100%;
  height: 100%;
  .operate-pannel{
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor{
      margin-right: 10px;
    }
    .el-input {
      width: 200px;
      margin-right: 10px;
    }
    .el-select{
      margin-right: 10px;
    }
  }
  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
.text {
  font-size: 14px;
}

.item {
  padding: 0;
}
</style>
