<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">部门：</span>
        <treeSelect
            style="width: 20%"
            :placeholder="'选择部门'"
            :multiple="false"
            v-model="queryParams.deptId"
            filterable
            :data="groupItem"
            @reflushTable="getXcbbList"
        />
        <span class="font-size14">姓名：</span>
        <el-input v-model="queryParams.userName" clearable placeholder="请输入姓名"
                  @change="handleChange"></el-input>

        <span class="font-size14">日期：</span>
        <el-date-picker
            v-model="queryParams.time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            @change="handleChange"
            style="width: 20%">
        </el-date-picker>
        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-search"
            v-if="this.$store.getters.permissions.indexOf('91310101') > -1"
            @click="getXcbbList"
        >查询
        </el-button>

        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="resetQuery"
        >重置
        </el-button>
        <el-button
            size="mini"
            type="text"
            class="rygf"
            icon="el-icon-download"
            v-if="this.$store.getters.permissions.indexOf('91310102') > -1"
            @click="openDialog('download')"
        >导出
        </el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>

      </div>
    </div>
    <div class="table-box">
      <Table
          :tableData="List"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          @getCurrentData="select"
          @row-dblclick="openDialog('edit')"
      >
        <template  slot-scope="scope">
          {{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}
        </template>
      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>
  </div>
</template>
<script>
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'
import {DownLoad, getDeptList, GetXcbbList} from "api/jfxc/xcbb";
import {downLoad, getFileNameTime} from "@/utils/tool";
import TreeSelect from "components/TreeSelect/treeSelect.vue";

export default {
  name: 'xcbb',
  components: {TreeSelect, Table, Pagination, Dropdown},

  created () {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  computed: {
    watermark(){
      return this.$store.state.user.watermark
    }
  },
  mounted () {
    this.getXcbbList()
    this.getDeptList();
  },
  data () {
    return {
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        userName: '',
        deptId: 0,
        time: [],
        StartTime: null,
        EndTime: null,
        type: 0
      },
      // 查询部门列表
      groupItem: [],
      // 车队长列表
      List: [],
      // 遮罩层
      loading: false,
      // 拷贝初始化查询参数
      defaultForm: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        userName: '',
        deptId: 1,
        time: [],
        StartTime: null,
        EndTime: null,
        type: 0
      },
      // 选择列表id
      selectID: 0,
      // 选择列表数据
      cbjlParams: {},
      realTableOptions:[],
      tableOptions: [
          //部门、人员、开始日期、结束日期、出行事由、地址
        {label: '部门', prop: 'deptName'},
        {label: '人员姓名', prop: 'userName'},
        {label: '开始日期', prop: 'planStartDate'},
        {label: '结束日期', prop: 'planEndDate'},
        {label: '出行事由', prop: 'note'},
        {label: '地址', prop:'address'}
      ],
      selectTree: [],
      labelData: "",
      valueData:"",
      defaultProps: {
        children: 'children',
        label: 'label'
      },
    }
  },
  methods: {
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },

    /** queryParam事件 */
    handleChange () {
      this.queryParams.pageNum = 1
      this.getXcbbList()
    },
    /** 查询插拔记录 */
    getXcbbList () {
      this.loading = true
      if (this.queryParams.time == null) {
        this.queryParams.planStartDate = null
        this.queryParams.planEndDate = null
      } else {
        this.queryParams.planStartDate = this.queryParams.time[0]
        this.queryParams.planEndDate = this.queryParams.time[1]
      }
      if(this.queryParams.deptId ==""){
        this.queryParams.deptId = -1;
      }
      GetXcbbList(this.queryParams).then((res) => {
        this.List = res.result.records
        this.queryParams.total = res.result.total
      }).finally(() => {
        this.loading = false
      })
    },
    /** 分页查询 */
    handleCurrentChange (val) {
      // debugger
      this.queryParams = val
      this.getXcbbList()
    },
    /** 重置查询 */
    resetQuery () {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm));
      this.queryParams.deptId = this.groupItem[0].id;
      this.getXcbbList()
    },
    /** 单击表事件 */
    select (row) {
      this.selectID = row.id
      this.cbjlParams = row
    },
    /** 打开新增弹窗 */
    openDialog (type) {
      switch (type) {
        case 'download':
          this.groupItem.forEach(value => {
            if (value.id === this.queryParams.deptId) {
              this.queryParams.groupName = value.groupname
            }
          })
          DownLoad(this.queryParams).then((res) => {
            let fileName = "";
            if(this.queryParams.userName){
              fileName = this.queryParams.userName;
            }
            if (this.queryParams.planStartDate){
              fileName = fileName + this.queryParams.planStartDate;
            }
            if (this.queryParams.planEndDate){
              fileName = fileName + "到" + this.queryParams.planEndDate;
            }
            downLoad(res, fileName+'行程报备.xlsx')

          })
          break
      }
    },

    handleNodeClick(data, self, child) {
      console.log('data', data)
      this.labelData = data.label;//展示部分
      this.valueData = data.id;//传参---id
    },
    getDeptList(){
      getDeptList().then((res)=>{
        this.groupItem = [];
        this.groupItem.push(res.result);
        //赋默认值，默认选中顶级节点
        this.queryParams.deptId = this.groupItem[0].id;
      })
    },

  }
}
</script>
<style lang="less" scoped>
.setstyle {
  min-height: 200px;
  padding: 0 !important;
  margin: 0;
  overflow: auto;
  cursor: default !important;
}
.search-container {
  width: 100%;
  height: 100%;
  .operate-pannel{
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor{
      margin-right: 10px;
    }
    .el-input {
      width: 200px;
      margin-right: 10px;
    }
    .el-select{
      margin-right: 10px;
    }
  }
  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
.text {
  font-size: 14px;
}

.item {
  padding: 0;
}
</style>
