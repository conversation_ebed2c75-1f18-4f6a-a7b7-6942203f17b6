<template>
  <div class="search-container">
    <div  class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">部门：</span>
        <treeSelect
            style="width: 15%"
            :placeholder="'选择部门'"
            :multiple="false"
            v-model="queryParams.deptId"
            filterable
            :data="groupItem"
            @reflushTable="GetXcjhList"
        />

        <span class="font-size14">姓名：</span>
        <el-input v-model="queryParams.userName" clearable placeholder="请输入姓名"
                  @change="handleChange"></el-input>

        <span class="font-size14">日期：</span>
        <el-date-picker
            v-model="queryParams.time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            @change="handleChange"
            style="width: 20%">
        </el-date-picker>
        <span  class="font-size14" >审核状态：</span>
        <el-select v-model="queryParams.approvingState" placeholder="请选择">
          <el-option
              v-for="item in approvingOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              @click.native ="handleChange">
          </el-option>
        </el-select>

        <span  class="font-size14" >执行状态：</span>
        <el-select v-model="queryParams.performState" placeholder="请选择">
          <el-option
              v-for="item in performOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              @click.native ="handleChange">
          </el-option>
        </el-select>
        <br/>
        <span class="font-size14" >确认状态：</span>
        <el-select v-model="queryParams.notarizeState" placeholder="请选择" style="margin-top: 5px;">
          <el-option
              v-for="item in notarizeStateOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              @click.native ="handleChange">
          </el-option>
        </el-select>

      </div>
    </div>
    <div class="operate-pannel">
      <div class="search-box">
        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-search"
            v-if="this.$store.getters.permissions.indexOf('JDWXC01JH01QX01') > -1"
            @click="GetXcjhList"
        >查询
        </el-button>

        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="resetQuery"
        >重置
        </el-button>
        <el-button
            v-if="this.$store.getters.permissions.indexOf('JDWXC01JH01QX02') > -1"
            size="mini"
            type="text"
            class="rygf"
            icon="el-icon-view"
            @click="openInfo()"
        >详情 </el-button>
        <el-button
            size="mini"
            type="text"
            class="rygf"
            icon="el-icon-download"
            v-if="this.$store.getters.permissions.indexOf('JDWXC01JH01QX03') > -1"
            @click="exportExcel()"
        >导出
        </el-button>
        <el-button
            size="mini"
            type="text"
            class="rygf"
            icon="el-icon-download"
            v-if="this.$store.getters.permissions.indexOf('JDWXC01JH01QX04') > -1"
            @click="exportExcelChildPlan()"
        >导出子计划详情
        </el-button>

        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>

      </div>
    </div>
    <div class="table-box">
      <Table
          :tableData="List"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          @getCurrentData="select"
          @row-dblclick="openDialog('edit')"
      >
        <template slot-scope="scope">
          {{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}
        </template>
        <template slot="ApprovingState" slot-scope="scope">
          <div v-if="scope.row.ApprovingState == 0"> 未提交 </div>
          <div v-if="scope.row.ApprovingState == 1"> 审核中 </div>
          <div v-if="scope.row.ApprovingState == 2"> 已驳回 </div>
          <div v-if="scope.row.ApprovingState == 3"> 已审核 </div>
          <div v-if="scope.row.ApprovingState == 4"> 已过期 </div>
          <div v-if="scope.row.ApprovingState == 5"> 已作废 </div>
        </template>
        <template slot="PerformState" slot-scope="scope">
          <div v-if="scope.row.PerformState == 0"> 未执行 </div>
          <div v-if="scope.row.PerformState == 1"> 执行中 </div>
          <div v-if="scope.row.PerformState == 2"> 已执行 </div>
          <div v-if="scope.row.PerformState == 3"> 已取消 </div>
        </template>
        <template slot="notarizeState" slot-scope="scope">
          <div v-if="scope.row.notarizeState === 0"> 未提交 </div>
          <div v-if="scope.row.notarizeState === 1"> 审核中 </div>
          <div v-if="scope.row.notarizeState === 2"> 已驳回 </div>
          <div v-if="scope.row.notarizeState === 3"> 确认通过 </div>
          <div v-if="scope.row.notarizeState === 4" @dblclick="handleDoubleClick(scope.row)"> 已作废 </div>
        </template>
      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, prev, pager, next, jumper"
          :total="queryParams.total"
      />

      <el-dialog :visible.sync="calcellationLogInfoVisible" width="20%" class="abow_dialog">
        <div  v-watermark="{label:watermark}">
          <p><strong>人员姓名：</strong>{{ calcellationLogInfo.userName }}</p>
          <p><strong>作废时间：</strong>{{ calcellationLogInfo.cancellationTime }}</p>
          <p><strong>作废类型：</strong>{{ calcellationLogInfo.type }}</p>
        </div>
      </el-dialog>

      <el-dialog title="详情" :visible.sync="dialogTableVisible" class="abow_dialog">
        <XchzDetail ref="xchzDetailDialog" :lc-id="cbjlParams.Id" @handleClose="dialogTableVisible = false"/>
      </el-dialog>
    </div>
  </div>
</template>getInfoByLcId
<script>
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'
import {
  DownLoad,
  getCalcellationLog,
  getDeptList,
  GetXcjhList,
  xcjhExportExcel,
  xcjhExportExcelChildPlan
} from "api/jfxc/xcjh";
import {downLoad, getFileNameTime} from "@/utils/tool";
import TreeSelect from "components/TreeSelect/treeSelect.vue";
import XchzDetail from "views/jfxc/xcjh/components/detail.vue";

export default {
  name: 'xcjh',
  components: {TreeSelect, Table, Pagination, Dropdown,XchzDetail},
  created() {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  computed: {
    watermark() {
      return this.$store.state.user.watermark
    }
  },
  mounted() {
    this.GetXcjhList()
    this.getDeptList();
  },
  data() {
    return {
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        userName: '',
        deptId: 0,
        time: [],
        StartTime: null,
        EndTime: null,
        type: 0,
        approvingState:-1,
        performState:-1,
        notarizeState:-1
      },
      // 查询部门列表
      groupItem: [],
      // 车队长列表
      List: [],
      // 遮罩层
      loading: false,
      // 拷贝初始化查询参数
      defaultForm: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        userName: '',
        deptId: 1,
        time: [],
        StartTime: null,
        EndTime: null,
        type: 0
      },
      // 选择列表id
      selectID: 0,
      // 选择列表数据
      cbjlParams: {},
      realTableOptions: [],
      tableOptions: [
        //部门、人员、开始日期、结束日期、出行事由、地址
        {label: '部门', prop: 'DeptName'},
        {label: '人员姓名', prop: 'UserName'},
        {label: '计划时间', prop: 'PlanTime'},
        {label: '出行事由', prop: 'Note'},
        {label: '计划里程', prop: 'PlanMileage'},
        {label: '计划费用', prop: 'PlanCost'},
        {label: '执行里程', prop: 'PerformMileage'},
        {label: '执行费用', prop: 'PerformCost'},
        {label: '审批状态', prop: 'ApprovingState',slot: true},
        {label: '执行状态', prop: 'PerformState',slot: true},
        {label: '确认状态', prop: 'notarizeState',slot: true},
        {label: '申请延期天数', prop: 'applyPostponeDays'},
        {label: '审批延期天数', prop: 'approvePostponeDays'}
      ],
      selectTree: [],
      labelData: "",
      valueData: "",
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      approvingOptions: [{value: -1, label: '全部'},{value: 0, label: '未提交'}, {value: 1, label: '已提交'}, {value: 2, label: '已驳回'}, {value: 3, label: '已审核'}, {value: 4, label: '已过期'}, {value: 5, label: '已作废'}],
      performOptions: [{value: -1, label: '全部'},{value: 0, label: '未执行'}, {value: 1, label: '执行中'}, {value: 2, label: '已执行'}, {value: 3, label: '已取消'}],
      notarizeStateOptions: [{value: -1, label: '全部'},{value: 0, label: '未提交'}, {value: 1, label: '审批中'}, {value: 2, label: '已驳回'}, {value: 3, label: '确认通过'},{value: 4, label: '已作废'}],
      dialogTableVisible: false,
      calcellationLogInfo:{
        userName:null,
        cancellationTime:null,
        type:"" //作废类型， 系统作废/手动作废
      },
      calcellationLogInfoVisible:false
    }
  },
  methods: {
    handleDoubleClick(row) {
      this.calcellationLogInfo = {
        userName:null,
        cancellationTime:null,
        type:"" //作废类型， 系统作废/手动作废
      }
      console.log(row)
      console.log(row.Id)
      //SystemCancellationTime ！=null 系统作废
      if (row.SystemCancellationTime == null){
        //读取信息
        getCalcellationLog({YwId:row.Id}).then(res=>{
          console.log(res.result)
          if(res.result != null){
            this.calcellationLogInfo = res.result
            this.calcellationLogInfo.type = '手动作废'
          }
        })
      }else {
        this.calcellationLogInfo.userName = row.UserName
        this.calcellationLogInfo.cancellationTime = row.SystemCancellationTime
        this.calcellationLogInfo.type = '系统作废'
      }
      this.calcellationLogInfoVisible = true

      // 弹出信息框
      //alert(`状态为已作废，ID: ${row.Id}`);
    },
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },

    /** queryParam事件 */
    handleChange() {
      this.queryParams.pageNum = 1
      this.GetXcjhList()
    },
    /** 查询记录 */
    GetXcjhList() {
      this.loading = true
      if (this.queryParams.time == null) {
        this.queryParams.planStartDate = null
        this.queryParams.planEndDate = null
      } else {
        this.queryParams.planStartDate = this.queryParams.time[0]
        this.queryParams.planEndDate = this.queryParams.time[1]
      }
      if (this.queryParams.deptId == "") {
        this.queryParams.deptId = -1;
      }
      GetXcjhList(this.queryParams).then((res) => {
        this.List = res.result.records
        this.queryParams.total = res.result.total
      }).finally(() => {
        this.loading = false
      })
    },
    /** 分页查询 */
    handleCurrentChange(val) {
      // debugger
      this.queryParams = val
      this.GetXcjhList()
    },
    /** 重置查询 */
    resetQuery() {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm));
      this.queryParams.deptId = this.groupItem[0].id;
      this.GetXcjhList()
    },
    /** 单击表事件 */
    select(row) {
      this.selectID = row.id
      this.cbjlParams = row
    },
    /** 导出excel **/
    exportExcel(){

      if (this.queryParams.time == null) {
        this.queryParams.planStartDate = null
        this.queryParams.planEndDate = null
      } else {
        this.queryParams.planStartDate = this.queryParams.time[0]
        this.queryParams.planEndDate = this.queryParams.time[1]
      }
      if (this.queryParams.deptId === "") {
        this.queryParams.deptId = -1;
      }

      xcjhExportExcel(this.queryParams).then((res)=>{
        let fileName ='行程计划' + getFileNameTime() + '.xlsx'
        downLoad(res, fileName)
      }).finally(() => {
        this.xcglLoading = false
      })
    },

    /** 导出excel **/
    exportExcelChildPlan(){

      if (this.queryParams.time == null) {
        this.queryParams.planStartDate = null
        this.queryParams.planEndDate = null
      } else {
        this.queryParams.planStartDate = this.queryParams.time[0]
        this.queryParams.planEndDate = this.queryParams.time[1]
      }
      if (this.queryParams.deptId === "") {
        this.queryParams.deptId = -1;
      }

      xcjhExportExcelChildPlan(this.queryParams).then((res)=>{
        let fileName ='行程子计划详情' + getFileNameTime() + '.xlsx'
        downLoad(res, fileName)
      }).finally(() => {
        this.xcglLoading = false
      })
    },

    /** 打开新增弹窗 */
    openDialog(type) {
      switch (type) {
        case 'download':
          this.groupItem.forEach(value => {
            if (value.id === this.queryParams.deptId) {
              this.queryParams.groupName = value.groupname
            }
          })
          DownLoad(this.queryParams).then((res) => {
            let fileName = "";
            if (this.queryParams.userName) {
              fileName = this.queryParams.userName;
            }
            if (this.queryParams.planStartDate) {
              fileName = fileName + this.queryParams.planStartDate;
            }
            if (this.queryParams.planEndDate) {
              fileName = fileName + "到" + this.queryParams.planEndDate;
            }
            downLoad(res, fileName + '行程报备.xlsx')

          })
          break
      }
    },

    handleNodeClick(data, self, child) {
      console.log('data', data)
      this.labelData = data.label;//展示部分
      this.valueData = data.id;//传参---id
    },
    getDeptList() {
      getDeptList().then((res) => {
        this.groupItem = [];
        this.groupItem.push(res.result);
        //赋默认值，默认选中顶级节点
        this.queryParams.deptId = this.groupItem[0].id;
      })
    },
    /**  查看详情  **/
    openInfo(){
      if (this.cbjlParams == null || this.cbjlParams == undefined || this.cbjlParams == ''){
        this.$message({
          showClose: true,
          message: '请选择一条数据',
          type: 'error',
          offset:400
        });
        return
      }

      this.dialogTableVisible = true;
      this.$refs.xchzDetailDialog.resetQuery();

      console.log("选中===")
      console.log( this.cbjlParams)
    },

  }
}
</script>
<style lang="less" scoped>
.setstyle {
  min-height: 200px;
  padding: 0 !important;
  margin: 0;
  overflow: auto;
  cursor: default !important;
}

.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }


  .table-box {
    height: calc(100% - 162px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}

.text {
  font-size: 14px;
}

.item {
  padding: 0;
}
</style>
