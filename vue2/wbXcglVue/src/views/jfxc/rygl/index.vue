<template>

  <div class="main" ref="main">


    <div class="list-box" style="width: 20%">
      <div class="operate-pannel">
        <div>
          <el-tree :data="data"
                   ref="tree"
                   :props="defaultProps"
                   node-key="id"
                   @node-click="handleNodeClick"
                   :expand-on-click-node="false"
                   :default-expanded-keys="[1]"
                   :highlight-current="true"
          />
        </div>

      </div>
    </div>
    <div class="process-info" style="width: 79%;height: 100%">
      <div class="search-container">
        <div class="operate-pannel">
          <div class="search-box">
            <span class="font-size14">姓名：</span>
            <el-input v-model="queryParams.realName" clearable placeholder="请输入姓名"
                      @change="handleChange"></el-input>

            <el-button
            size="mini"
            type="text"
            icon="el-icon-search"
            @click="queryByRealName()"
            v-if="this.$store.getters.permissions.indexOf('JDWXC01RY01QX01') > -1"
            >
              查询
            </el-button>

            <el-button
                size="mini"
                type="text"
                icon="el-icon-plus"
                v-if="this.$store.getters.permissions.indexOf('JDWXC01RY01QX02') > -1"
                @click="add()"

            >新增
            </el-button>

            <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                v-if="this.$store.getters.permissions.indexOf('JDWXC01RY01QX3') > -1"
                @click="del()"
            >删除
            </el-button>
            <el-button
                size="mini"
                type="text"
                icon="el-icon-check"
                v-if="this.$store.getters.permissions.indexOf('JDWXC01RY01QX4') > -1"
                @click="enable()"
            >启用
            </el-button>
            <el-button
                size="mini"
                type="text"
                icon="el-icon-close"
                v-if="this.$store.getters.permissions.indexOf('JDWXC01RY01QX5') > -1"
                @click="disable()"
            >停用
            </el-button>
            <el-button
                size="mini"
                type="text"
                icon="el-icon-download"
                v-if="this.$store.getters.permissions.indexOf('JDWXC01RY01QX6') > -1"
                @click="exportExcelTemplate()"
            >导出Excel模板
            </el-button>
            <el-button
                size="mini"
                type="text"
                icon="el-icon-download"
                v-if="this.$store.getters.permissions.indexOf('JDWXC01RY01QX6') > -1"
                @click="exportExcel()"
            >导出Excel
            </el-button>
            <el-button
                type="text"
                size="mini"
                icon="el-icon-upload2"
                v-if="this.$store.getters.permissions.indexOf('JDWXC01RY01QX7') > -1"
                @click="openFileUpload">
              导入Excel
            </el-button>

            <el-button
                size="mini"
                type="text"
                icon="el-icon-data-analysis"
                v-if="this.$store.getters.permissions.indexOf('JDWXC01RY01QX8') > -1"
                @click="showEndData"
            >终点数据
            </el-button>

            <el-button
                v-if="this.$store.getters.permissions.indexOf('JDWXC01RY01QX9') > -1"
                size="mini"
                type="text"
                icon="el-icon-close"
                @click="expenseSubsidyLimitLog()"
            >查看费用补贴上限日志
            </el-button>

            <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
          </div>
        </div>
        <div class="table-box">

          <Table
              :tableData="xcglList"
              :tableOptions="realTableOptions"
              :loading="xcglLoading"
              @getCurrentData="select"
              @rowDblClick="dbclick"
              :cell-class-name="tableCellClassName"
          >
            <template slot-scope="scope">
              {{
                (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
              }}
            </template>

            <template slot="State" slot-scope="scope">
              <div v-if="scope.row.State == 1">
                启用
              </div>
              <div v-if="scope.row.State == 0">
                禁用
              </div>

            </template>

            <template slot="CostTopLimit" slot-scope="scope">

              <!--v-if去判断双击的是不是当前单元格-->
              <!-- 增加权限标识判断 -->
              <el-input
                  @blur="updateNotesValue(scope.row)"
                  :ref="'row'+ scope.row.Id +','+ scope.column.index"
                  v-model="scope.row.NewCostTopLimit"
                  v-if="'row'+ scope.row.Id +','+scope.column.index == currentCell && (this.$store.getters.permissions.indexOf('JDWXC01RY01QX10') > -1)">
              </el-input>
              <span v-else>{{ scope.row.CostTopLimit }}</span>

            </template>

          </Table>
          <Pagination
              @handleRefresh="handleCurrentChange"
              :queryParam="queryParams"
              layout="total, prev, pager, next, jumper"
              :total="queryParams.total"
          />
        </div>
      </div>

      <el-dialog  title="新增人员" :visible.sync="dialogTableVisible" class="abow_dialog">
          <addPersionTable ref="persionTable" @handleSubmit="handleSubmit" @handleClose="dialogTableVisible = false"/>
      </el-dialog>

      <el-dialog title="费用补贴日志" :visible.sync="expenseSubsidyLimitLogVisible" class="abow_dialog">
        <ExpenseSubsidyLimitLogTable ref="subsidyLimitLogRef" :personId="clbgParams.SrcUserId" @handleClose="expenseSubsidyLimitLogVisible = false"/>
      </el-dialog>

      <el-dialog title="终点数据" :visible.sync="endDataVisible" width="45%" class="abow_dialog" style="height: 50vh">
        <div v-watermark="{ label: watermark }">
          <el-row v-if="this.$store.getters.permissions.indexOf('913408') > -1">
            <el-col>
              <el-button @click="cleanEndData">清空终点数据</el-button>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <p><strong>人员姓名：</strong>{{ clbgParams.RealName }}</p>
              <p><strong>监理 ID：</strong>{{ personInfo.JlId }}</p>
              <p><strong>当天最后点区域监理 ID：</strong>{{ personInfo.EndArrivedJlId }}</p>
            </el-col>
            <el-col :span="12">
              <p><strong>当天最后点距离公司里程：</strong>{{ personInfo.EndMileage }}</p>
              <p><strong>当天最后点距离公司费用：</strong>{{ personInfo.EndCost }}</p>
              <p><strong>当天最后点距离公司进入时间：</strong>{{ personInfo.EndArrivedTime }}</p>
            </el-col>
          </el-row>
        </div>
      </el-dialog>

      <div v-show="false">
        <el-upload
            ref="fileupload"
            accept=".xls,.xlsx"
            name="file"
            :showUploadList="false"
            :multiple="false"
            :auto-upload="true"
            action=""
            :beforeUpload="beforeUpload"
            :http-request="uploadHttpRequest"
        >
          <el-button type="text" size="mini" class="uploac-button">
            导入excel
          </el-button>
        </el-upload>
      </div>

    </div>

  </div>

</template>
<script>
import {mapActions} from 'vuex'
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'
import {getDeptList} from '@/api/jfxc/zzjg'
import addPersionTable from "./components/addPersionTable";

import SplitPane from "@/components/split-pane";
import {
  cleanEndData,
  CreatPerson,
  DeleteById,
  DisablePersonById, ExportExcel,
  ExportExcelTemplate, getPersonInfo,
  GetPersonList, ImportExcel, InsertLimitByPersonId,
  StartPersonById
} from "api/jfxc/rygl";
import {downLoad, getFileNameTime} from "@/utils/tool";
import ExpenseSubsidyLimitLogTable from "./components/expenseSubsidyLimitLog";

export default {
  name: 'index',
  components: {ExpenseSubsidyLimitLogTable, SplitPane, Table, Pagination, Dropdown,addPersionTable},
  data() {
    return {
      data: [],
      dtDialog: false,
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      labelPosition: 'right',
      formLabelAlign: {
        name: '',
        status: '',
        type: ''
      },
      flag: false,
      clbgParams: {},
      dialogTableVisible: false,
      dialogTableVisibleUpd: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      realTableOptions: [],
      tableOptions: [
        {label: '名称', prop: 'RealName'},
        {label: '组织', prop: 'DeptName'},
        {label: '短号', prop: 'Sphone'},
        {label: '联系电话', prop: 'Telephone'},
        {label: '补贴费用上限', prop: 'CostTopLimit',slot: true},
        {label: '状态', prop: 'State',slot: true}
      ],
      xcglLoading: false,
      xcglList: [],
      selectID : 0,
      expenseSubsidyLimitLogVisible:false,
      // 用一个字符串来保存当前双击的是哪一个单元格
      currentCell: null,

      endDataVisible:false,
      personInfo:{},

    };
  },
  computed: {
    watermark() {
      return this.$store.state.user.watermark
    }
  },
  methods: {
    ...mapActions(['GetInfo']),
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },


    showEndData(){

      if (this.selectID  === 0){
        return
      }
      this.endDataVisible = true
      this.getPersonInfo()
    },

    getPersonInfo(){
      this.personInfo = {}

      getPersonInfo(this.selectID).then(res=>{
        this.personInfo = res.result
      })
    },

    //清理终点数据
    cleanEndData(){
      cleanEndData({id:this.personInfo.Id}).then(res=>{
        if (res.code === 200){
          this.$message.success(res.message);
          this.getPersonInfo()
        } else {
          this.$message.success(res.text);
        }
      })
    },



    /** queryParam事件 */
    handleChange() {
      this.queryParams.pageNum = 1
      this.queryByRealName()
    },
    //部门点击
    handleNodeClick(data, checked) {
      if (checked) {
        this.$refs.tree.setCheckedNodes([data])
        this.selectNode = data;
        this.queryParams.deptId = this.$refs.tree.getCheckedKeys()[0];
        this.queryParams.pageNum = 1;
        this.queryParams.pageSize = 10
        this.queryParams.deptId = data.id
        console.log(data)
        console.log(this.queryParams.deptId)
        this.getPersonList();
      }
    },
    /** 分页查询 */
    handleCurrentChange(val) {
      this.queryParams = val
      this.getPersonList()
    },
    getPersonList() {
      if (!this.queryParams.deptId || -1 == this.queryParams.deptId ){
        this.$message.error("请选择部门！");
        return;
      }
      this.xcglLoading = true
      GetPersonList(this.queryParams).then((res) => {
        this.xcglList = res.result.records
        this.queryParams.total = res.result.total
      }).finally(() => {
        this.xcglLoading = false
      })
    },

    /** 单击表事件 */
    select (row) {
      this.selectID = row.Id
      this.clbgParams = row
    },


    // 给单元格绑定横向和竖向的index，这样就能确定是哪一个单元格
    tableCellClassName({ row, column, rowIndex, columnIndex }) {
      row.index = rowIndex;
      column.index = columnIndex;
      row.mcolumn = column;
    },
    // 获得当前双击的单元格的横竖index，然后拼接成一个唯一字符串用于判断，并赋给currentCell
    // 拼接后类似这样："1,0","1,1",
    dbclick(row, column) {
      this.currentCell = 'row'+ row.Id +','+column.index;
      // 这里必须要setTimeout，因为在点击的时候，input才刚被v-if显示出来，不然拿不到dom
      setTimeout(() => {
        // 双击后自动获得焦点
        this.$nextTick(()=>{
          this.$refs['row'+ row.Id +','+column.index].focus();
        })
        // row增加一个新的参数，将原先的值赋给新的参数，用来比较是否进行更改
        this.$set(row, 'NewCostTopLimit', row.CostTopLimit)
      })
    },
    // 当input失去焦点的时候，隐藏input, 将修改的内容传给后台
    updateNotesValue(row) {
      this.currentCell = null
      // 比较这个单元格的值是否进行了更改，没有更改不进行任何操作，更改了才触发接口
      if (row.CostTopLimit !== row.NewCostTopLimit) {
        var oldVale = row.CostTopLimit;
        row.CostTopLimit = row.NewCostTopLimit
        InsertLimitByPersonId(row).then( res =>{
          //console.log(res);
          if (res.success){
            this.$message.success(res.message)
          }else {
            this.$message.error("编辑失败")
            //还原原本的值
            row.CostTopLimit = oldVale;
          }
        })
      }
    },





    queryByRealName(){
      this.xcglLoading = true
      GetPersonList(this.queryParams).then((res) => {
        this.xcglList = res.result.records
        this.queryParams.total = res.result.total
      }).finally(() => {
        this.xcglLoading = false
      })
    },

    add(){
      if (!this.queryParams.deptId || -1 == this.queryParams.deptId ){
        this.$message.error("请选择部门！");
        return;
      }
      this.dialogTableVisible = true;
      this.$refs.persionTable.resetQuery();
    },

    //删除
    del(){
      if (this.selectID == 0) {
        this.$message.error("请选择行!")
        return;
      }
      const ids = [];
      ids.push(this.clbgParams.Id)
      DeleteById(ids).then((res)=>{
        if (res.code==200){
          this.$message.success(res.message)
          this.getPersonList();
        }else {
          this.$message.error(res.message)
        }
      })
    },
    openFileUpload(){
      this.$refs.fileupload.$el.querySelector('.uploac-button').click();
    },

    //查看费用补贴日志
    expenseSubsidyLimitLog(){
      if (this.selectID == 0){
        this.$message.error("请选择行！");
        return;
      }
      this.expenseSubsidyLimitLogVisible = true;
      this.$refs.subsidyLimitLogRef.resetQuery()
    },


    handleSubmit(data){
      //提取id
      let ids = [];
      for (let i = 0; i < data.length; i++) {
        ids.push(data[i].id);
      }
      CreatPerson({ids:ids,deptId:this.queryParams.deptId}).then((res)=>{

        if(res.success){
          this.$message.success(res.message);
          //刷新列表
          this.getPersonList();
          this.dialogTableVisible = false;
        }else {
          this.$message.error(res.message)
        }
      })
    },

    //启用
    enable(){
     if (this.selectID == 0) {
       this.$message.error("请选择行!")
       return;
     }
      StartPersonById(this.clbgParams.Id).then((res)=>{
        if (res.success){
          this.$message.success("操作成功！")
          this.getPersonList();
        }else {
          this.$message.error(res.message)
        }
      })
    },

    //停用
    disable(){
      if (this.selectID == 0) {
        this.$message.error("请选择行!")
        return;
      }
      DisablePersonById(this.clbgParams.Id).then((res)=>{

        if (res.success){
          this.$message.success("操作成功！")
          this.getPersonList();
        }else {
          this.$message.error(res.message)
        }
      })
    },

    //导出excel模板
    exportExcelTemplate(){
      ExportExcelTemplate().then((res)=>{
        let fileName ='人员管理导入模板' + getFileNameTime() + '.xlsx'
        downLoad(res, fileName)
      })
    },

    //导出excel
    exportExcel(){
      // if (!this.queryParams.deptId || -1 == this.queryParams.deptId || !this.queryParams.realName ){
      //   this.$message.error("请选择部门或者输入姓名!");
      //   return;
      // }
      ExportExcel(this.queryParams).then((res)=>{
        let fileName ='人员管理' + getFileNameTime() + '.xlsx'
        downLoad(res, fileName)
      })
    },

    //导入excel
    beforeUpload(file){
      // 获取上传excel文件的信息
      const fileContent = file.raw;
      // 获取文件类型
      const types = file.name.split(".")[1];
      const fileType = ["xlsx", "xls"].some(
          (item) => item === types
      );
      if (!fileContent) {
        if (!fileType) {
          alert("格式错误！请重新选择");
          return;
        }
      }
    },

    uploadHttpRequest(item) {

      const form = new FormData()
      form.append('file', item.file)
      ImportExcel(form).then(res => {

        if (res.code==200){
          this.$message.success(res.message)
          this.getPersonList();
        }else {
          this.$message.error(res.message)
        }
        this.$refs.fileupload.clearFiles();//清空原来上传的文件

      }).catch(err => {
        //this.$message.error("okokokokokkokok")
        this.$refs.fileupload.clearFiles();//清空原来上传的文件

      })
    },




    getDeptList(){
      getDeptList().then((res) => {
        let a = [];
        a.push(res.result)
        this.data = a
      }).finally(() => {
      })
    }
  },





  created() {
    this.getDeptList()
    //console.log(this.$store.getters.permissions)
    //console.log(this.$store.state.user.permissions)
  }
};
</script>
<style lang="scss" scoped>

.main {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: space-between;
}


.list-box {
  height: 100%;
  box-shadow: 0 0 6px #d9e2f1aa;
  border-radius: 10px;
  padding: 10px;
  display: flex;
  flex-direction: column;
  transition: all .5s;

  .operate-pannel {
    height: 100%;
    width: 100%;

    justify-content: space-between;
    align-items: center;
    overflow: auto;
    background: #FFFFFF;
  }

}

.process-info {
  height: 100%;
  box-shadow: 0 0 6px #d9e2f1aa;
  border-radius: 10px;
  padding: 10px;
  transition: all .5s;
}

.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 100px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}
.abow_dialog {
  display: flex;
  justify-content: center;
  align-items: Center;
  overflow: hidden;
  .el-dialog {
    margin: 0 auto !important;
    height: 90%;
    overflow: hidden;
    .el-dialog__body {
      position: absolute;
      left: 0;
      top: 54px;
      bottom: 0;
      right: 0;
      padding: 0;
      z-index: 1;
      overflow: hidden;
      overflow-y: auto;
    }
  }
}

</style>
