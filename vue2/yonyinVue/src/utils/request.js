import axios from 'axios'
import { Notification, MessageBox, Message, Loading } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'

// router.afterEach((to, from, next) => { document.querySelector("body").setAttribute("style", "overflow: auto !important;") });
axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8'
// axios.defaults.headers['x-access-token'] = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyTmFtZSI6IjExMDMyMDA0IiwiZXhwIjoxNzQ2Njg3ODUyLCJpYXQiOjE2ODM2MTU4NTJ9.GFfQoIbamoTXOm1HEy5Wdor4NdAc5b37OgWLoFFFqfA'
// 创建axios实例
const service = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: process.env.VUE_APP_BASE_API,
  // 超时
  timeout: 100000
})
service.interceptors.request.use(
  config => {
    if (getToken()) {
      config.headers.Authorization = getToken() // 让每个请求携带自定义token 请根据实际情况自行修改
    }
    // else{
    //   router.push('/login')
    // }
    // else{
      // store.dispatch('LogOut').then(() => {
        // location.reload()
      // })
    // }
    return config
  },
  error => {
    Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(res => {
  const code = res.data.code
  if (code === 401) {
    MessageBox.confirm('登录状态已过期，您可以继续留在该页面，或者重新登录','系统提示', {
    confirmButtonText: '重新登录',
    cancelButtonText: '取消',
    type: 'warning' }).then(() => {
    store.dispatch('LogOut').then(() => {
        location.reload()
      })
    })
  }
  // else if (code !== 200) {
  //   if(code == undefined){
  //     return res.data
  //   }else{
  //     Message.error(res.data.text || res.data.message)
  //     return Promise.reject(res.data.text || res.data.message)
  //   }
  // }
  else {
    return res.data
  }
},
error => {
  Message({
    message: error.message,
    type: 'error',
    duration: 5 * 1000
  })
  return Promise.reject(error)
}
)
export default service
