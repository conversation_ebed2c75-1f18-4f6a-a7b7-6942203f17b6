<template>
  <div class="navbar" :style="{backgroundColor: theme}">
    <template v-if="!sidebar">
      <img :src="logo" class="title-logo" />
      <div class="breadcrumb-title text-weight font-size20">数字化平台</div>
    </template>
    
    <div class="right-menu">
      <template>
        <search class="right-menu-item" style="width:300px"/>

        <div class="right-menu-item hover-effect">
          <el-popover 
            placement="bottom"
            title="数字化平台运维热线"
            width="200"
            trigger="click"
            popper-class="text-center">
            <div class="content-contain" style="position:relative">
              <div style="height: 30px">
                0574-51103708
              </div>
              <i class="iconfont font-size28" style="position:absolute; right:0;bottom:0">&#xe623;</i>
            </div>
            <i class="iconfont font-size24" slot="reference">&#xe88b;</i>
          </el-popover>
        </div>

        <div class="right-menu-item hover-effect">
          <theme-picker @themeChange="themeChange"/>
        </div>
        
        <size-select id="size-select" class="right-menu-item hover-effect" />
        
        <div class="right-menu-item hover-effect">
          <el-popover
            placement="bottom"
            trigger="click"
            popper-class="text-center">
            <div style="display:flex">
              <div>
                <div class="font-size14">安卓APP下载</div>
                <img src="@/assets/images/downloadApp.png" width="120"/>
              </div>
              <div>
                <div class="font-size14">苹果APP下载</div>
                <img src="@/assets/images/iosDownload.jpg" width="120"/>
              </div>
            </div>
            <i class="iconfont font-size20" slot="reference">&#xe605;</i>
          </el-popover>
        </div>
        <div class="right-menu-item">
          <i class="el-icon-date font-size18"></i>
          <span class="font-size14" style="margin-left:4px">{{currentDate}}</span>
        </div>
      </template>

      <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
        <div class="avatar-wrapper">
          <img :src="avatar" class="user-avatar">
          <i class="iconfont font-size12 dropdown-icon">&#xe771;</i>
        </div>
        <el-dropdown-menu slot="dropdown">
          <!-- <router-link to="/user/profile">
            <el-dropdown-item>个人中心</el-dropdown-item>
          </router-link>
          <el-dropdown-item>
            <span>修改信息</span>
          </el-dropdown-item> -->
          <el-dropdown-item v-show="canChangePerson" @click.native="personChangeVisible = true">
            <span>用户切换</span>
          </el-dropdown-item>
          <el-dropdown-item divided @click.native="logout">
            <span>退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <div class="right-menu-item font-size14">
        {{name}}
      </div>
    </div>

    <el-dialog
      title="用户快速切换选择"
      width="30%"
      :visible.sync="personChangeVisible"
      destroy-on-close>
      <el-radio-group class="radio-group" v-model="personOption">
        <el-radio
          v-for="item in changePersonOptions"
          :key="item.REC_ID"
          :label="item.RC_USER"
          class="radio-option">
          {{item.RC_USERN}}
        </el-radio>
      </el-radio-group>
      <div class="confirm-btn">
        <el-button type="primary" @click="handleChangePerson">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import logoImg from '@/assets/logo.png'
import { mapGetters } from 'vuex'
import dayjs from 'dayjs'
import SizeSelect from '@/components/SizeSelect'
import Search from '@/components/HeaderSearch2'
import ThemePicker from '@/components/ThemePicker'
import {getCurrentUccOptionList} from '@/api/home'
export default {
  components: {
    SizeSelect,
    Search,
    ThemePicker
  },
  data(){
    return {
      personChangeVisible:false,
      changePersonOptions:[],
      personOption:'',
    }
  },
  computed: {
    ...mapGetters([
      'avatar','name','sidebar'
    ]),
    theme() {
      return this.$store.state.settings.theme;
    },
    currentDate(){
      return dayjs().format('YYYY-MM-DD')
    },
    logo(){
      return logoImg
    },
    //无可切换用户 不显示该按钮
    canChangePerson(){
      return this.changePersonOptions.length ? true : false
    }
  },
  mounted(){
    //获取用户切换列表
    getCurrentUccOptionList().then(res=>{
      this.changePersonOptions = res.Table || []
    })
  },
  methods: {
    //切换用户
    handleChangePerson(){
      this.$store.dispatch('changeLogin',this.personOption).then(res=>{
        this.personChangeVisible = false
        this.$router.go(0)
      })
    },
    
    
    //左侧菜单打开或收缩
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },

    async logout() {
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('LogOut').then(() => {
          this.$router.push({path: '/login'});
        })
      }).catch(() => {});
    },

    themeChange(val){
      this.$store.dispatch('settings/changeSetting', {
        key: 'theme',
        value: val
      })

      localStorage.setItem('layout-theme',val)
    },
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  // box-shadow: 0 1px 4px rgba(0,21,41,.08);
  padding: 0 20px;
  flex-shrink: 0;
  border-bottom: #ffffff55 1px solid;

  .hamburger{
    line-height: 46px;
    height: 100%;
    float: left;
    color: #fff;
  }

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color:transparent;

    &:hover {
      background: rgba(0, 0, 0, .025)
    }
  }

  .title-logo{
    margin: 10px 0;
    float: left;
    height: 32px;
  }
  .breadcrumb-title{
    color: #fff;
    float: left;
    line-height: 50px;
  }

  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      color: #fff;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background .3s;

        &:hover {
          background: rgba(0, 0, 0, .025)
        }
      }
    }

    .avatar-container {

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 20px;
        }
        .dropdown-icon{
          position: absolute;
          right: -4px;
          bottom: 0;
        }
      }
    }
  }

  .radio-group{
    display:flex;
    flex-direction:column;

    .radio-option{
      margin-bottom: 10px;
    }
  }

  .confirm-btn{
    text-align: right;
  }
}
</style>
