<template>
    <div class="has-logo" id="sidebar" style="background-color: #fff">
        <logo :collapse="isCollapse" />
        <el-scrollbar wrap-class="scrollbar-wrapper">
            <el-menu
                :default-active="activeMenu"
                :collapse="isCollapse"
                text-color="#000"
                :unique-opened="true"
                active-text-color="#000"
                :collapse-transition="false"
                mode="vertical">
                <component
                    v-for="route in routes"
                    :key="route.name"
                    :index="route.name"
                    :is="(route.children && route.children.length > 0) ? 'el-submenu' : 'el-menu-item'">
                    <template slot="title">
                        <i class="iconfont font-size14" style="margin-right:10px">&#xe790;</i>
                        <span slot="title" class="font-size14">{{route.name}}</span>
                    </template>
                    <template v-if="route.children && route.children.length > 0">
                        <el-menu-item
                            v-for="routeChild in route.children"
                            :key="routeChild.name"
                            :index="routeChild.path"
                            :disabled="!routeChild.hasPermi"
                            @click="handleOpenModule(routeChild)">
                            <span slot="title">{{routeChild.name}}</span>
                            <!-- <template slot="title">
                                <el-link :href="baseURL + routeChild.path" target="_blank" :underline="false" class="font-size14">
                                    {{routeChild.name}}
                                </el-link>
                            </template> -->
                        </el-menu-item>
                    </template>
                </component>
            </el-menu>
        </el-scrollbar>
        <div class="toggle-btn" @click="toggleSideBar">
            <i :class="isCollapse ? 'el-icon-arrow-right' : 'el-icon-arrow-left'"/>
        </div>
    </div>
</template>

<script>
import { mapGetters, mapState } from "vuex";
import { isExternal } from '@/utils/validate'
import {OpenMoudleWindow} from '@/utils/jiaohu'
import Logo from "./Logo";

export default {
    components: { Logo},
    data(){
        return {
            baseURL:process.env.VUE_APP_BASE_API,
        }
    },
    computed: {
        ...mapState(["settings"]),
        ...mapGetters(["sidebar","routes"]),
        activeMenu() {
            const route = this.$route;
            const { meta, path } = route;
            // if set path, the sidebar will highlight the path you set
            if (meta.activeMenu) {
                return meta.activeMenu;
            }
            return path;
        },
        isCollapse() {
            return !this.sidebar;
        },
    },
    methods:{
        toggleSideBar() {
            this.$store.dispatch('settings/toggleSideBar')
        },
        resolvePath(routePath){
            if (isExternal(routePath)) {
                return routePath
            }
            return routePath
        },
        //打开应用
        handleOpenModule(routeInfo){
            const {module_mix, path} = routeInfo
            OpenMoudleWindow(module_mix, path)
        }
    }
};
</script>
