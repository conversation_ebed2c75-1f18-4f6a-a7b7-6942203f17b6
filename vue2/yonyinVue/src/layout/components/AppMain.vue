<template>
  <section class="app-main" :style="{background: `linear-gradient(to bottom, ${theme} 40%, #eaeff3 40% 100%)`}">
    <transition name="fade-transform" mode="out-in">
      <keep-alive>
        <router-view v-if="!$route.meta.link" :key="key" />
      </keep-alive>
    </transition>
  </section>
</template>

<script>

export default {
  name: 'AppMain',
  computed: {
    key() {
      return this.$route.path
    },
    theme(){
      return this.$store.state.settings.theme
    },
  }
}
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  height: 100%;
  width: 100%;
  position: relative;
  overflow: hidden;
  flex-grow: 1;
  display: flex;
}

.fixed-header + .app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    // min-height: calc(100vh - 84px);
    
    /* 删除了navbar */
    min-height: calc(100vh - 50px);
  }

  .fixed-header + .app-main {
    padding-top: 50px;
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 17px;
  }
}
</style>
