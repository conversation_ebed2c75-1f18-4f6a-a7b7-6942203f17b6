<template>
  <div class="main" ref="main">
    <div class="page">
      <SplitPane @resized="resized">
        <div slot="part1" class="box-common" v-watermark="{label:watermark}">
          <schedule/>
        </div>
        <div slot="part2" class="box-common" v-watermark="{label:watermark}">
          <Signboard ref="signboardRef" />
        </div>
        <div slot="part3" class="box-common" v-watermark="{label:watermark}">
          <Notify/>
        </div>
        <div slot="part4" class="box-common" ref="activeBox" v-watermark="{label:watermark}">
          <ActiveChart ref="activeChartRef"/>
        </div>
      </SplitPane>
    </div>
  </div>
</template>
  
<script>
import schedule from "./components/schedule";
import SplitPane from "@/components/split-pane";
import Signboard from '@/components/Signboard';
import Notify from './components/IndexNotify';
import ActiveChart from './components/ActiveChart'
import { mapGetters } from "vuex";
import {cwMap0,cwMap0Func,SafeOpenFunction,closePages} from '@/utils/jiaohu'
export default {
  components: {
    SplitPane,
    Notify,
    Signboard,
    ActiveChart,
    schedule
  },
  computed:{
    ...mapGetters(['sidebar']),
    watermark(){
      return this.$store.state.user.watermark
    }
  },
  mounted(){
    // if(window.parent){
    //   window.parent.postMessage({
    //     data: 'test'
    //   }, '*')
    // }
    let sidebarElement = document.getElementById('sidebar')
    let el = document.createElement('fakeElement')
    //浏览器兼容
    let transitions = {
      'transition':'transitionend',
      'OTransition':'oTransitionEnd',
      'MozTransition':'transitionend',
      'WebkitTransition':'webkitTransitionEnd'
    }
    let whichTransitionEvent = ()=>{
      for(let key in transitions){
        if(el.style[key] !== undefined) return transitions[key]
      }
    }
    let transitionEvent = whichTransitionEvent()
    //监听左侧sidebar宽度动画完成，echarts resize
    transitionEvent && sidebarElement.addEventListener(transitionEvent,(e)=>{
      if(e.propertyName === 'width') this.resized()
    },false)

    window.addEventListener('message', event => this.getMessage(event));
    window.addEventListener('beforeunload', e => closePages())
    
  },
  destroyed(){
    window.removeEventListener('message', event => this.getMessage(event))
    window.removeEventListener('beforeunload', e => closePages())
  },
  methods: {
    //尺寸拖动修改后 echarts图重绘
    resized(event){
      this.$refs['activeChartRef'].resize();
      this.$refs.signboardRef.resize();
    },
    getMessage(event){
      // console.log("message",event.source == this.window);
      var jsonData = event.data;
      if((jsonData!=undefined&&jsonData!=null) && jsonData.msgtype==='wpsfm') {
          var mid=jsonData.mid;
          if (mid != "" && mid != undefined) {
              var cwt = cwMap0.get(mid);
              var cwf = cwMap0Func.get(mid);
              if (cwt != undefined && cwt != null && cwf != undefined && cwf != null) {
                SafeOpenFunction(cwt, cwf.fid, cwf.furl, cwf.ftitle);
              }
          }
      }
    }
  },
};
</script>
  
<style scoped lang="scss">
.splitpanes__pane {
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: Helvetica, Arial, sans-serif;
  color: rgba(255, 255, 255, 0.6);
  font-size: 5em;
}

.main {
  width: 100%;
  height: 100%;
}

.page {
  border-radius: 10px;
  padding: 20px;
  margin: 30px;
  background-color: #fff;
  height: calc(100% - 60px);
  box-sizing: border-box;
}

.box-common {
  padding: 15px;
  border: 1px solid #d9e2f1;
  border-radius: 10px;
  width: 100%;
  height: 100%;
}

.right_bottom {
  width: 100%;
  height: 100%;
}
</style>