<template>
    <div class="active-container" ref="activeContainRef">
        <div class="active-title">
            <div class="font-size18 text-weight">用户活跃度</div>
            <div class="right-circle" :style="{ border: `${theme} 3px solid` }"></div>
        </div>

        <div ref="chartRef" style="height:90%; width:100%"></div>
    </div>

</template>

<script>
import * as echarts from 'echarts'
import {getURDList} from '@/api/home'
export default {
    name: 'activeChart',
    data() {
        return {
            chart: null,
            activeData: []
        }
    },
    computed: {
        theme() {
            return this.$store.state.settings.theme
        }
    },
    mounted() {
        this.getActiveData()
        window.addEventListener('resize', () => {
            this.resize()
        })
    },
    methods: {
        getActiveData(){
            getURDList().then(res=>{
                this.activeData = (res.Table || []).map(item=>{
                    return {
                        name: item.compName,
                        value: (item.compRVal/item.compUVal * 100).toFixed(1),
                        compUVal: item.compUVal
                    }
                })
                this.chart = echarts.init(this.$refs['chartRef'])
                this.initChart()
            })
        },
        initChart() {
            if(!this.activeData.length) this.chart = null
            else{
                this.$nextTick(()=>{
                    const x = [], y = [], userNum = []
                    let proMax = []
                    this.activeData.map((item,index)=>{
                        if(index < 10){
                            x.unshift(item.name)
                            y.unshift(item.value)
                            userNum.unshift(item.compUVal)
                            proMax[index] = 100
                        }
                    })
                    const options = {
                        grid: {
                            left: '2%',
                            right: '0',
                            bottom: '2%',
                            top: '2%',
                            containLabel: true
                        },
                        tooltip: {
                            trigger: "axis",
                            formatter: '{b}: {c}%'
                        },
                        xAxis: {
                            type: 'value',
                            minInterval: 1,
                            axisLine: {show: false},
                            axisLabel: {show: false},
                            axisTick: {show: false},
                            splitLine: {show: false}
                        },
                        yAxis: [{
                            type: 'category',
                            data: x,
                            axisLine: {show: false},
                            axisTick: {show: false},
                            splitLine: {show: false}
                        }, {
                            show: true,
                            data: y,
                            position: 'right',
                            axisLabel:{
                                formatter:(value)=>{
                                    return value + '%'
                                }
                            },
                            axisLine: {show: false},
                            axisTick: {show: false},
                            splitLine: {show: false}
                        }
                        // {
                        //     data: userNum,
                        //     position: 'right',
                        //     axisLabel:{
                        //         formatter:(value)=>{
                        //             return `\r\r\r\r\r\r\r\r\r\r(` + value + ')'
                        //         }
                        //     },
                        //     axisLine: {show: false},
                        //     axisTick: {show: false},
                        //     splitLine: {show: false},
                        // }
                        ],
                        series: [
                            {
                                type: 'bar',
                                barWidth: 8,
                                zlevel: 1,
                                data: y,
                                itemStyle: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                                        { offset: 1, color: '#e1127c' },
                                        { offset: 0.5, color: '#7244ff' },
                                        { offset: 0, color: '#2b9bff' }
                                    ]),
                                    borderRadius: [15, 15, 15, 15]
                                },
                            }, {
                                name: '背景',
                                type: 'bar',
                                barWidth: 8,
                                barGap: '-100%',
                                data: proMax,
                                itemStyle: {
                                    color: '#dfe5f1',
                                    borderRadius: 30,
                                },
                            }
                        ]
                    }
                    this.chart.clear()
                    this.chart.setOption(options, true)
                })
                
            }
            

        },
        resize() {
            if(this.chart){
                this.chart.resize()
                this.initChart()
            }
        }
    }

}
</script>

<style lang="scss" scoped>
.active-container {
    height: 100%;
    width: 100%;

    .active-title {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .right-circle {
            height: 16px;
            width: 16px;
            border-radius: 8px;
        }
    }
}
</style>