<template>
    <div style="height:100%;width:100%">
        <div class="title-line">
          <div class="font-size18 text-weight">通知公告</div>
          <!-- <div class="font-size12 text-grey pointer" @click="getMoreNotify">
              <span>更多</span>
              <i class="el-icon-d-arrow-right "/>
          </div> -->
        </div>
        <div class="notify-contain" v-if="notifyList.length">
            <div
                v-for="(item, index) in notifyList"
                :key="index"
                class="notify-item">
                <div class="title-views">
                    <div 
                        class="notify-title font-size14 pointer"
                        :style="item.isHover ? {color: theme} : {}"
                        @mouseover="item.isHover = true"
                        @mouseout="item.isHover = false"
                        @click="handleGetDetail(item)">
                        {{`【${item.State}】${item.Title}`}}
                    </div>
                    <!-- <div class="font-size12 text-grey">
                        <i class="el-icon-view"/>
                        {{item.views || 0}}
                    </div> -->
                </div>
                
                <div class="notify-info font-size12 text-grey">
                    <div>{{formatDate(item.Sdate)}}</div>
                    <div>{{item.Sgroupname}}</div>
                </div>
            </div>
        </div>
        <div v-else class="font-size14 notify-empty text-grey">
            -- 暂无通知公告 --
        </div>
        <el-dialog :visible.sync="detailDialog">
            <div v-watermark="{label:watermark}">
                <div class="font-size18 text-weight detail-title">
                    {{this.notifyItem.Title}}
                </div>
                <el-divider></el-divider>
                <div v-html="this.notifyItem.Content"></div>
                <div class="detail-remark text-weight">{{this.notifyItem.Sgroupname}}</div>
                <div class="detail-remark text-weight">{{formatDate(this.notifyItem.Sdate)}}</div>
                <el-divider></el-divider>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import dayjs from 'dayjs'
import {getUserEmailList, getEmailDetailList} from '@/api/home'
export default {
    name: 'notify',
    data(){
        return {
            notifyList:[],
            notifyItem:{},
            detailDialog:false
        }
    },
    computed:{
        theme(){
            return this.$store.state.settings.theme
        },
        watermark(){
            return this.$store.state.user.watermark
        }
    },
    mounted(){
        this.getNotifyList()
    },
    methods:{
        formatDate(date){
            return dayjs(date).format('YYYY-MM-DD')
        },
        async getNotifyList(){
            await getUserEmailList().then(res=>{
                this.notifyList = res.Table || []
                this.notifyList = this.notifyList.map((item,index)=> {
                    return {...item, isHover:false}
                })
            })
        },
        handleGetDetail(item){
            this.notifyItem = item
            this.detailDialog = true
            // getEmailDetailList(item.Id).then(res=>{

            // })
        },
        getMoreNotify(){
            
        },

    }

}
</script>

<style lang="scss" scoped>
.title-line{
    display: flex;
    // justify-content: space-between;
    // align-items: center;
}

.notify-empty{
    text-align: center;
    margin: 10px 0;
}

.notify-contain{
    height: 90%;
    position: relative;
    overflow-x: hidden;
}
.notify-item{
    border-bottom: #e2e2e2 1px solid;
    padding: 10px;
    .title-views{
        display: flex;
        // justify-content: space-between;
        // align-items: center;
        margin-bottom: 5px;
    
        .notify-title{
            // width: 80%;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
        }

    }
    
    .notify-info{
        display: flex;
        justify-content: space-between;
    }
}
::v-deep .el-dialog__header{
    padding: 0;
}
::v-deep .el-dialog__body{
    color:#000;
    max-height: 80vh;
    overflow-y: auto;
}
.detail-title{
    text-align: center;
    margin-bottom: 10px;
}
.detail-remark{
    text-align: right;
    margin-top: 10px;
}
</style>