<template>
  <div class="schedule">
    <el-row>
      <div class="font-size18" style="float: left">
        <strong>待办事项</strong>
      </div> 
      <div style="float: right">
        <search @handleSearch="handleSearch"/>
      </div>
    </el-row>
    <br />
    <div class="content" v-if="queryList.length">
      <el-collapse accordion>
        <el-collapse-item v-for="item in queryList" :key="item.lcid" @click.native="handleGetDBDetail(item)">
          <template slot="title">
            <div style="width:100%;font-weight:bold">
              <li style="float:left" class="font-size20">{{ item.lcname }}</li>
              <li align="right" class="font-size20 item" :style="{color:theme,padding:'0 30px 0 0'}">{{ item.lcnum }}<span class="font-size16">项</span></li>
            </div>
          </template>
          <div
            v-for="(detail, index) in dbDetailList"
            :key="index"
            class="text-weight detail-item"
            @click="handleClickItem(detail)">
            <div class="item-content">{{ detail.LcProjName }}</div>
            <span class="item-date">{{ formatDate(detail.startdate) }}</span>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
    <div v-else class="db-empty font-size14 text-grey">-- 暂无待办事项 --</div>
    <el-dialog
      :visible.sync="dialogVisible"
      width="80%">
      <template slot="title">
        <div class="left-circle" :style="{ border: `${theme} 3px solid` }"></div>
        <div class="font-size16">{{iframeParams.LcName}}</div>
      </template>
      <iframe 
        class="iframe-contain" 
        :src="baseURL + `/netframe/Page/NFrame/DBProx.html?v=${iframeParams.vrandom}&lcid=${iframeParams.lc_jdid}&ywid=${iframeParams.ywid}&lcurl=${iframeParams.lcurl}`">
      </iframe>
    </el-dialog>
  </div>
</template>
<script>
import Search from "@/components/HeaderSearch";
import {getScheduleList, getDBDetailData} from "@/api/home"
import { mapGetters } from "vuex";
import dayjs from "dayjs";
export default {
  components: {
    Search,
  },
  computed: {
    ...mapGetters(["avatar"]),
    theme() {
      return this.$store.state.settings.theme;
    },
    
  },
  data() {
    return {
      dialogVisible:false,
      dbSearch:'',
      queryList: [],
      dbDetailList:[],
      baseURL: process.env.VUE_APP_BASE_API,
      iframeParams:{}
    };
  },
  mounted(){
    this.getList()
  },
  methods: {
    formatDate(date) {
      return dayjs(date).format("YYYY-MM-DD");
    },
    handleSearch(val){
      this.dbSearch = val
      this.getList()
    },
    //获取待办列表
    getList(){
      const params = {
        searchtj: this.dbSearch
      }
      getScheduleList(params).then(res=>{
        if(res.code == 200){
          this.queryList = res.dbslist || []
        }else{
          this.$message.error(res.text)
        }
      })
    },
    handleGetDBDetail(item){
      getDBDetailData(item.lcid).then(res=>{
        if(res.code == 200){
          this.dbDetailList = res.dbdlist || []
        }else{
          this.$message.error(res.text)
        }
      })
    },
    handleClickItem(detail){
      this.dialogVisible = true
      this.iframeParams = {
        vrandom:Math.random(),
        LcName:detail.LcName,
        lc_jdid:detail.lc_jdid,
        ywid:detail.ywID,
        lcurl:detail.LcProjUrl
      }
    }
  },
};
</script>
<style scoped>
.schedule {
  width:100%;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.el-collapse,.el-collapse-item__wrap {
  border-top: none;
}
::v-deep .el-collapse-item__arrow{
  display: none;
}

</style>
<style lang="scss" scoped>
  ::v-deep .el-dialog{
    margin-top: 4px !important;
    margin-bottom: 0;
    .el-dialog__header{
      height: 40px;
      padding: 10px;
      padding-bottom: 5px;
      display: flex;
      align-items: center;
      
      .left-circle{
        height: 16px;
        width: 16px;
        border-radius: 8px;
        margin-right: 4px;
      }
      .el-dialog__headerbtn{
        top: 10px;
      }
    }
    .el-dialog__body{
      padding: 0 10px; 
      height: calc(100vh - 50px);
    }
  }

  .iframe-contain{
    width: 100%;
    height: 100%;
    border: none;
  }
  .content {
    // height: 100px;
    list-style: none;
    margin: 0px;
    // padding: 16px 0;
    overflow-x: auto;
    display: inline-block;
    // white-space: nowrap;
    width: 100%; 
    // background: #F9FAFD;

    .detail-item{
      padding: 10px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .item-content{
        width: calc(100% - 70px);
      }

      // .item-date{
      //   width: 20%;
      // }
    }
  }

  .db-empty{
    text-align: center;
  }
  .nav-item-scroll {
    background: #E5F0FF;
    color: #24252E;
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    padding: 8px 8px 180px;
    text-align: center;
    display: inline;
    // margin: 0 4px 0;
    border-radius: 16px;
  }
  // .content::-webkit-scrollbar {
  //   width: 10px;
  //   height: 10px;
  // }
  // .content::-webkit-scrollbar-thumb {
  //   border-radius: 50px;
  //   background: #eee;
  // }
  // .content::-webkit-scrollbar-track {
  //   // box-shadow: inset 0 0 2px #333;
  //   border-radius: 300em;
  //   padding: 5px;
  //   background: transparent;
  // }
</style>
<style>
  .text.v-textarea textarea::-webkit-resizer {
    background: pink;
  }
</style>
