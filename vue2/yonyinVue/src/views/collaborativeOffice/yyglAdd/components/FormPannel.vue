<template>
    <el-form :model="yyglForm" :rules="rules" ref="yyglForm" label-width="100px" v-watermark="{label: watermark}">
        <el-row>
            <el-col :span="8">
                <el-form-item label="需求描述" prop="type">
                    <el-input v-if="btnType==='查看'" v-model="yyglForm.type" readonly/>
                    <el-select v-else v-model="yyglForm.type">
                        <el-option 
                            v-for="option in typeOptions"
                            :key="option.id"
                            :label="option.content"
                            :value="option.content"/>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="8">
                <el-form-item label="用印呈送" prop="yysend">
                    <el-input v-if="btnType==='查看'" v-model="yyglForm.yysend" readonly/>
                    <el-select v-else v-model="yyglForm.yysend">
                        <el-option
                            v-for="option in yysenOptions"
                            :key="option.id"
                            :label="option.content"
                            :value="option.content"/>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="8">
                <el-form-item label="项目编号" prop="bh">
                    <el-input v-model="yyglForm.bh" readonly/>
                </el-form-item>
            </el-col>
        </el-row>
        <template v-if="yyglForm.type === '用印申请'">
            <el-row>
                <el-col :span="16">
                    <el-form-item label="印章类型" prop="yzfl">
                        <el-input v-if="btnType==='查看'" v-model="yyflStr" readonly/>
                        <el-select v-else v-model="yyglForm.yzfl" multiple style="width:100%">
                            <el-option
                                v-for="option in yzflOptions"
                                :key="option.id"
                                :label="option.content"
                                :value="option.content"/>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="用印份数" prop="yycount">
                        <el-input v-model="yyglForm.yycount" :readonly="btnType==='查看'"/>
                    </el-form-item>
                </el-col>
            </el-row>
        </template>
        <template v-else-if="yyglForm.type.includes('外借')">
            <el-form-item label="外借期限" required style="margin-bottom: 0">
                <el-row>
                    <el-col :span="7">
                        <el-form-item prop="jystartdate">
                            <el-date-picker
                                v-model="yyglForm.jystartdate"
                                type="date"
                                placeholder="选择开始日期"
                                :readonly="btnType==='查看'"
                                style="width:96%">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item prop="sdatequantum">
                            <el-input v-if="btnType==='查看'" v-model="yyglForm.sdatequantum" readonly/>
                            <el-select v-else v-model="yyglForm.sdatequantum" placeholder="请选择开始时间">
                                <el-option label="上午" value="上午"></el-option>
                                <el-option label="下午" value="下午"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="2"><div style="text-align:center">至</div></el-col>
                    <el-col :span="7">
                        <el-form-item prop="jyedndate">
                            <el-date-picker
                                v-model="yyglForm.jyedndate"
                                type="date"
                                placeholder="选择结束日期"
                                :readonly="btnType==='查看'"
                                style="width:96%">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item prop="edatequantum">
                            <el-input v-if="btnType==='查看'" v-model="yyglForm.edatequantum" readonly/>
                            <el-select v-else v-model="yyglForm.edatequantum" placeholder="请选择结束时间">
                                <el-option label="上午" value="上午"></el-option>
                                <el-option label="下午" value="下午"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form-item>
        </template>
        <el-form-item label="申请理由" prop="yysy">
            <el-input 
                type="textarea" 
                v-model="yyglForm.yysy" 
                :readonly="btnType==='查看'"
                maxlength="50" 
                show-word-limit 
                placeholder="简要描述，不超过50字"/>
        </el-form-item>
        <el-form-item>
            <div style="text-align: right">
                <el-button v-if="btnType !== '查看'" type="primary" @click="handleSubmit('yyglForm')">保存</el-button>
                <el-button @click="$emit('handleClose')">关闭</el-button>
            </div>
        </el-form-item>
    </el-form>
</template>

<script>
import {getYyglType, getYySend} from '@/api/yygl'
export default {
    name:'FormPannel',
    props:{
        btnType:{
            type:String,
            default:'新增'
        }
    },
    computed:{
        watermark(){
            return this.$store.state.user.watermark
        }
    },
    data(){
        return {
            yyglForm:{
                type: '用印申请',//需求描述 默认值
                yysend: '',//用印呈送
                bh: '', //项目编号
                yzfl: ['公司公章'],//印章类型 默认值
                jystartdate: '',//外借期限 开始年月日
                jyedndate: '',//外借期限 结束年月日
                sdatequantum: '',//外借期限 开始上下午
                edatequantum: '',//外借期限 结束上下午
                yycount: 1,//用印份数 默认值
                yysy:'',//申请理由
            },
            yyflStr:'',
            rules:{
                type: [{required: true, message: '请选择需求描述', trigger:'change'}],
                yysend: [{required: true, message: '请选择用印呈送', trigger:'change'}],
                yysy:[{required: true, message: '请填写申请理由', trigger:'blur'}],
                yzfl:[{required: true, message: '请选择印章类型', trigger:'change'}],
                yycount:[{required: true, message:'请填写用印份数', trigger:'blur'}],
                jystartdate:[{required: true, message: '请选择开始日期', trigger:'change'}],
                sdatequantum: [{required: true, message: '请选择开始时间', trigger:'change'}],
                jyedndate: [{required: true, message: '请选择结束日期', trigger:'change'}],
                edatequantum: [{required: true, message: '请选择结束时间', trigger:'change'}],
            },
            typeOptions:[],
            yysenOptions:[],
            yzflOptions:[]
        }
    },
    mounted(){
        if(this.btnType !== '查看') this.getOptions()
    },
    methods:{
        async getOptions(){
            //id=66 获取需求描述
            await getYyglType(66).then(res=>{
                this.typeOptions = res || []
            })
            //id=65 获取印章类型
            await getYyglType(65).then(res=>{
                this.yzflOptions = res || []
            })
            //获取用印呈送
            await getYySend().then(res=>{
                this.yysenOptions = res || []
            })
        },
        //提交表单数据
        handleSubmit(formName){
            this.$refs[formName].validate((valid)=>{
                if(valid){
                    const {type} = this.yyglForm
                    if(type === '用印申请'){
                        this.yyglForm.jystartdate = ''
                        this.yyglForm.sdatequantum = ''
                        this.yyglForm.jyedndate = ''
                        this.yyglForm.edatequantum = ''
                    }else if(type.includes('外借')){
                        this.yyglForm.yzfl = ''
                        this.yyglForm.yycount = ''
                    }else{
                        this.yyglForm.jystartdate = ''
                        this.yyglForm.sdatequantum = ''
                        this.yyglForm.jyedndate = ''
                        this.yyglForm.edatequantum = ''
                        this.yyglForm.yzfl = ''
                        this.yyglForm.yycount = ''
                    }
                    this.$emit('handleSubmit', this.yyglForm)
                }else{
                    this.$message({
                        type:'warning',
                        message:'提交失败！请确认表单是否填写完整！'
                    })
                    return false
                }
            })
        },
        //初始化
        initData(data){
            if(data.id){
                this.yyglForm = {...data}
                this.yyglForm.yzfl = data.yzfl.split('、')
                this.yyflStr = data.yzfl
            }
        },
        
    }
}
</script>

<style lang="scss" scoped>
</style>