<template>
    <div class="yygl-container">
        <div class="list-box" :style="isFold ? {width: '96%'} :{width: '69%'}">
            <div class="operate-pannel">
                <div>
                    <el-button type="text" icon="el-icon-plus" v-hasPermi="['910106']" @click="handleOpenDialog('新增')">新增</el-button>
                    <el-button type="text" icon="el-icon-edit" v-hasPermi="['910107']" @click="handleOpenDialog('修改')">修改</el-button>
                    <el-button type="text" icon="el-icon-delete" v-hasPermi="['910108']" @click="handleDelete">删除</el-button>
                    <el-button type="text" icon="el-icon-view" @click="handleOpenDialog('查看')">查看</el-button>
                    <el-button type="text" icon="el-icon-upload2" v-hasPermi="['910109']" @click="handleOpenSubmitDialog">提交</el-button>
                    <el-button type="text" icon="el-icon-document" v-hasPermi="['910110']" @click="handleAppendixDialog">附件</el-button>
                </div>
                <div class="search-box">
                    <el-input placeholder="用印编号、需求描述、用印呈送" v-model="searchInput" clearable/>
                    <el-button type="text" icon="el-icon-search" @click="getTableList">查询</el-button>
                    <el-button type="text" icon="el-icon-refresh" @click="getTableList">刷新</el-button>
                    <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
                </div>
            </div>
            <div class="table-box">
                <Table
                    ref="yyglTable"
                    :tableData="tableData"
                    :tableOptions="realTableOptions"
                    :loading="loading"
                    :queryParam="queryParam"
                    @getCurrentData="getCurrentData">
                    <template slot="yycount" slot-scope="scope">
                        {{scope.row.yycount == 0 ? '' : scope.row.yycount}}
                    </template>
                </Table>
                <Pagination
                    :total="total"
                    :queryParam="queryParam"
                    @handleRefresh="handleRefresh"
                />
            </div>
        </div>
        <div :class="isFold ? 'fold-style':'process-info'">
            <i 
                class="fold-icon" 
                :class="isFold ? 'el-icon-s-fold' : 'el-icon-s-unfold'" 
                @click="isFold = !isFold"/>
            <div v-show="!isFold" style="height:100%">
                <div class="process-title">流程信息</div>
                <div class="process-table-box">
                    <Table
                        ref="processTable"
                        :tableData="processTableData"
                        :tableOptions="processTableOptions"
                        :loading="processLoading"
                        @getCurrentData="getCurrentProcessData">
                        <template slot="transdate" slot-scope="scope">
                            {{formatDate(scope.row.transdate)}}
                        </template>
                    </Table>
                </div>
                <div class="opinion-process">
                    <div class="font-size14" style="width: 100px">流程意见：</div>
                    <div class="opinion-text">{{remark}}</div>
                </div>
                <div class="process-title">附件信息</div>
                <div class="appendix-table-box">
                    <Table
                        :tableData="appendixTableData"
                        :tableOptions="appendixTableOptions"
                        :loading="appendixLoading">
                        <template slot="operate" slot-scope="scope">
                            <el-button type="text" @click="downloadFile(scope.row)">下载</el-button>
                        </template>
                    </Table>
                </div>
            </div>
        </div>
        <el-dialog :title="btnType" :visible.sync="dialogFormVisible" destroy-on-close>
            <FormPannel
                ref="yyglForm"
                :btnType="btnType"
                @handleSubmit="handleSubmit"
                @handleClose="dialogFormVisible = false"/>
        </el-dialog>

        <el-dialog title="表单" :visible.sync="submitDialogVisible" destroy-on-close>
            <SubmitForm
                ref="submitForm"
                :rowData="currentRowData"
                @handleSubmit="handleProcessSubmit"
                @handleClose="submitDialogVisible = false"/>
        </el-dialog>

        <el-dialog title="附件管理" :visible.sync="dialogAppendixVisible">
            <UploadFile @importFile="importFile"/>
            <el-button type="text" icon="el-icon-delete" @click="deleteFile">删除</el-button>
            <Table
                :tableData="appendixTableData"
                :tableOptions="appendixTableOptions"
                :loading="appendixLoading"
                @getCurrentData="getCurrentFileData"
                style="min-height:250px">
                <template slot="operate" slot-scope="scope">
                    <el-button type="text" @click="downloadFile(scope.row)">下载</el-button>
                </template>
            </Table>
        </el-dialog>
    </div>
</template>
<script>
import Table from '../components/MainTable'
import Pagination from '../components/Pagination'
import UploadFile from '../components/UploadFile'
import SubmitForm from '../components/SubmitForm'
import FormPannel from './components/FormPannel'
import Dropdown from '../components/ColumnDropdown'
import {
    getyyglList,
    queryYygl,
    addYygl,
    editYygl,
    deleteYygl,
    getYyglFile,
    addYyglFile,
    deleteYyglFile,
    downloadYyglFile,
    getWorkFlowList,
    submitFlow,
    getCanSubmitWjDate
} from '@/api/yygl'
import dayjs from 'dayjs'
import fileDownload from 'js-file-download'
export default {
    name:'index',
    components:{Table,Pagination,UploadFile,SubmitForm,FormPannel,Dropdown},
    data(){
        return {
            searchInput: '',
            isFold: false,
            tableData:[],
            tableOptions:[
                {label: '项目编号', prop:'bh', width: 130},
                {label: '需求描述', prop:'type'},
                {label: '申请理由', prop:'yysy'},
                {label: '用印呈送', prop:'yysend'},
                {label: '印章类型', prop:'yzfl'},
                {label: '用印份数', prop:'yycount', width: 100, slot:true},
                {label: '外借期限', prop:'wjDate', width: 160},
            ],
            realTableOptions:[],
            queryParam: {
                current: 1,
                size: 10,
            },
            total:0,
            loading:false,
            processTableData:[],
            processTableOptions:[
                {label: '部门', prop: 'groupname', sortable:false},
                {label: '流程名称', prop: 'lcJdmc', sortable:false},
                {label: '处理人', prop: 'personname', sortable:false},
                {label: '处理日期', prop: 'transdate', width: 90, sortable:false, slot: true},
                {label: '意见', prop: 'feed', sortable:false, width: 50},
            ],
            processLoading:false,
            dialogFormVisible: false, //新增修改表单显示
            btnType: '新增',//默认新增按钮
            currentSelectId:'',
            currentRowData:{},
            remark:'',//流程意见
            submitDialogVisible: false,
            dialogAppendixVisible:false,//附件管理弹窗
            appendixLoading: false,
            appendixTableData:[],
            appendixTableOptions:[
                {label: '文件名', prop: 'filename'},
                {label: '操作', prop: 'operate', sortable:false, width: 150, slot: true}
            ],
            currentFileSelectId:'',
        }
    },
    mounted(){
        this.getTableList()
    },
    methods:{
        formatDate(date){
            return date ? dayjs(date).format('YYYY-MM-DD') : date
        },
        //获取新表格列表
        getNewArr(newTableOptions){
            this.realTableOptions = [...newTableOptions]
            // this.$refs.yyglTable.doLayout()
        },
        //获取表格数据
        async getTableList(){
            this.loading = true
            const params = {...this.queryParam, CID: this.searchInput}
            await getyyglList(params).then(res=>{
                this.tableData = res.records || []
                this.total = res.total || 0
                this.$refs.yyglTable.setCurrent(this.tableData[0])
                this.loading = false
            }).catch(err=>{
                this.loading = false
            })
        },
        //模糊查询和刷新
        handleRefresh(queryParam){
            this.queryParam = {...queryParam}
            this.getTableList()
        },
        //打开表单弹窗
        handleOpenDialog(type){
            if(type !== '新增' && !this.currentSelectId){
                this.$message({
                    type: 'warning',
                    message: '请先选择一条数据！'
                })
                return 
            }
            this.dialogFormVisible = true
            this.btnType = type
            if(type !== '新增'){
                //修改和查看初始化数据
                queryYygl(this.currentSelectId).then(res=>{
                    this.$refs['yyglForm'].initData(res || {})
                }).catch(err=>{
                    this.$message({
                        type:'error',
                        message:'获取数据失败！'
                    })
                })
            }
        },
        //获取当前点击的数据id
        getCurrentData(val){
            this.currentSelectId = val.id
            this.currentRowData = val
            //获取流程信息
            this.handleGetWorkFlowData()
            //获取附件信息
            this.handleGetAppendixData()
        },
        //获取流程信息表格中点击数据的id
        getCurrentProcessData(val){
            this.remark = val.feed
        },
        //新增和修改完成后提交
        async handleSubmit(data){
            const {yzfl, jystartdate, jyedndate, yycount} = data
            let submitData = {...data}
            submitData.yzfl = yzfl ? yzfl.join('、') : ''
            submitData.yycount = yycount ? yycount * 1 : ''
            submitData.jystartdate = jystartdate ? dayjs(jystartdate).format('YYYY-MM-DD') : ''
            submitData.jyedndate = jyedndate ? dayjs(jyedndate).format('YYYY-MM-DD') : ''

            if(this.btnType === '新增'){
                await addYygl(submitData).then(res=>{
                    if(res){
                        this.$message({
                            type: 'success',
                            message: '新增数据成功！'
                        })
                    }else{
                        this.$message({
                            type: 'success',
                            message: '新增数据失败！'
                        })
                    }
                })
                this.dialogFormVisible = false
                this.getTableList()
            }else{
                await editYygl(submitData).then(res=>{
                    if(res){
                        this.$message({
                            type: 'success',
                            message: '修改数据成功！'
                        })
                    }else{
                        this.$message({
                            type: 'error',
                            message: '修改数据失败！'
                        })
                    }
                })
                this.dialogFormVisible = false
                this.getTableList()
            }
        },
        //删除一条数据
        handleDelete(){
            if(!this.currentSelectId){
                this.$message({
                    type: 'warning',
                    message: '请先选择一条数据！'
                })
                return 
            }
            this.$confirm('确认删除该条记录？').then(async _=>{
                await deleteYygl(this.currentSelectId).then(res=>{
                    if(res){
                        this.$message({
                            type: 'success',
                            message: '删除数据成功！'
                        })
                    }else{
                        this.$message({
                            type: 'error',
                            message: '删除数据失败！'
                        })
                    }
                })
                this.getTableList()
            }).catch(_=>{
                this.$message({
                    type: 'info',
                    message: '已取消删除！'
                })
            })
            
        },
        //获取流程信息
        async handleGetWorkFlowData(){
            this.processLoading = true
            await getWorkFlowList(this.currentSelectId).then(res=>{
                this.processTableData = res || []
                this.$refs.processTable.setCurrent(this.processTableData[0])
                this.processLoading = false
            }).catch(err=>{
                this.processLoading = false
            })
        },
        //打开提交表单弹窗
        handleOpenSubmitDialog(){
            if(!this.currentSelectId){
                this.$message({
                    type: 'warning',
                    message: '请先选择一条数据！'
                })
                return 
            }
            getCanSubmitWjDate(this.currentSelectId).then(res=>{
                if(res === true){
                    this.submitDialogVisible = true
                    this.$nextTick(()=>{
                        //获取发送人数据
                        this.$refs['submitForm'].getFlowData()
                    })
                }else{
                     this.$message({
                        type: 'warning',
                        message: res
                    })
                }
            })
        },
        //提交流程
        async handleProcessSubmit(val){
            const params={
                lcjdid: 910201,
                ywid: this.currentSelectId,
                ...val
            }
            await submitFlow(params).then(res=>{
                if(res.success){
                    this.$message({
                        type: 'success',
                        message: '提交成功！'
                    })
                }
            })
            this.submitDialogVisible = false
            this.getTableList()
        },
        //打开附件弹窗
        handleAppendixDialog(){
            if(!this.currentSelectId){
                this.$message({
                    type: 'warning',
                    message: '请先选择一条数据！'
                })
                return 
            }
            this.dialogAppendixVisible = true
        },
        //获取附件数据列表
        async handleGetAppendixData(){
            this.appendixLoading = true
            await getYyglFile(this.currentSelectId).then(res=>{
                this.appendixTableData = res || []
                this.appendixLoading = false
            }).catch(err=>{
                this.appendixLoading = false
            })
             this.currentFileSelectId = ''
        },
        //新增附件
        async importFile(formData){
            formData.append('id',this.currentSelectId)
            await addYyglFile(formData).then(res=>{
                if(res.success === true){
                    this.$message({
                        type: 'success',
                        message: res.text
                    })
                }else{
                    this.$message({
                    type: 'error',
                    message: res.text
                })
                }
            })
            this.handleGetAppendixData()
        },
        //获取点击附件id
        getCurrentFileData(val){
            this.currentFileSelectId = val.id
        },
        //删除附件
        deleteFile(){
            if(!this.currentFileSelectId){
                this.$message({
                    type: 'warning',
                    message: '请先选择一条数据！'
                })
                return 
            }
            this.$confirm('确认删除该条记录？').then(async _=>{
                await deleteYyglFile(this.currentFileSelectId).then(res=>{
                    if(res){
                        this.$message({
                            type: 'success',
                            message: '删除附件成功！'
                        })
                    }else{
                        this.$message({
                            type: 'error',
                            message: '删除附件失败！'
                        })
                    }
                })
                this.handleGetAppendixData()
            }).catch(_=>{
                this.$message({
                    type: 'info',
                    message: '已取消删除！'
                })
            })
            
        },
        //下载附件
        downloadFile(row){
            downloadYyglFile(row.id).then(res=>{
                fileDownload(res, row.filename)
            })
        },
    }
}
</script>
<style lang="scss" scoped>
.list-box{
    height: 100%;
    box-shadow: 0 0 6px #d9e2f1aa;
    border-radius: 10px;
    padding: 10px;
    display: flex;
    flex-direction: column;
    transition: all .5s;
    .operate-pannel{
        height: 50px;
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .search-box{
            display: flex;
            .el-input{
                width: 220px;
                margin-right: 5px;
            }
        }
    }

    .table-box{
        width: 100%;
        height: calc(100% - 100px);

        ::v-deep .el-pagination{
            margin-top: 10px;
            text-align:center;
        }
    }
}

.fold-icon{
    cursor: pointer;
    float:right;
}

.process-info{
    width: 30%;
    height: 100%;
    box-shadow: 0 0 6px #d9e2f1aa;
    border-radius: 10px;
    padding: 10px;
    transition: all .5s;

    .process-title{
        height: 30px;
        font-weight: bold;
    }
    .process-table-box{
        width: 100%;
        height: 50%;
    }

    .opinion-process{
        margin: 10px 0;
        display: flex;

        .opinion-text{
            width: 100%;
            height: 60px;
            overflow-y: auto;
            border: #dfe6ec 1px solid;
            padding: 4px;
        }
    }

    .appendix-table-box{
        height: calc(50% - 150px);
    }
}
//折叠后样式
.fold-style{
    width: 0 !important;
    height: 100%;
    padding: 10px;
    transition: all .5s
}

</style>