<template>
    <div class="search-container">
        <div class="operate-pannel">
            <div>
                <el-button type="text" icon="el-icon-view" @click="handleOpenDialog">查看</el-button>
                <el-button type="text" icon="el-icon-document" @click="handlePreview">电子表格</el-button>
                <el-button type="text" icon="el-icon-download" @click="handleExport('word')">导出为word</el-button>
                <el-button type="text" icon="el-icon-download" @click="handleExport('pdf')">导出为PDF</el-button>
            </div>
            <div class="search-box">
                <el-input placeholder="用印编号、需求描述、用印呈送" v-model="searchInput" clearable/>
                <el-button type="text" icon="el-icon-search" @click="getTableList">查询</el-button>
                <el-button type="text" icon="el-icon-refresh" @click="getTableList">刷新</el-button>
                <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
            </div>
        </div>
        <div class="table-box">
            <Table 
                ref="yyglSearchTable"
                :tableData="tableData"
                :tableOptions="realTableOptions"
                :loading="loading"
                :queryParam="queryParam"
                @getCurrentData="getCurrentData">
                <template slot="yycount" slot-scope="scope">
                    {{scope.row.yycount == 0 ? '' : scope.row.yycount}}
                </template>
            </Table>
            <Pagination
                :total="total"
                :queryParam="queryParam"
                @handleRefresh="handleRefresh"
            />
        </div>

        <el-dialog
            title="流程过程信息"
            :visible.sync="dialogVisible"
            fullscreen>
            <el-tabs type="border-card">
                <el-tab-pane label="基本信息">
                    <el-row :gutter="20">
                        <el-col :span="3"><span>需求描述：</span></el-col>
                        <el-col :span="9">
                            <el-input v-model="currentRowData.type" readonly></el-input>
                        </el-col>
                        <el-col :span="3"><span>项目编号：</span></el-col>
                        <el-col :span="9">
                            <el-input v-model="currentRowData.bh" readonly></el-input>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="3"><span>借用类型：</span></el-col>
                        <el-col :span="9">
                            <el-input v-model="currentRowData.qblx" readonly></el-input>
                        </el-col>
                        <el-col :span="3"><span>用印呈送：</span></el-col>
                        <el-col :span="9">
                            <el-input v-model="currentRowData.yysend" readonly></el-input>
                        </el-col>
                    </el-row>
                    <el-row v-if="currentRowData.type === '用印申请'" :gutter="20">
                        <el-col :span="3"><span>印章类型：</span></el-col>
                        <el-col :span="9">
                            <el-input v-model="currentRowData.yzfl" readonly></el-input>
                        </el-col>
                        <el-col :span="3"><span>用印份数：</span></el-col>
                        <el-col :span="9">
                            <el-input v-model="currentRowData.yycount" readonly></el-input>
                        </el-col>
                    </el-row>
                    <el-row v-else-if="(currentRowData.type || '').includes('外借')" :gutter="20">
                        <el-col :span="3"><span>外借期限：</span></el-col>
                        <el-col :span="7">
                            <el-input v-model="currentRowData.jystartdate" readonly></el-input>
                        </el-col>
                        <el-col :span="3">
                            <el-input v-model="currentRowData.sdatequantum" readonly></el-input>
                        </el-col>
                        <el-col :span="1"><div style="text-align:center">至</div></el-col>
                        <el-col :span="7">
                            <el-input v-model="currentRowData.jyedndate" readonly></el-input>
                        </el-col>
                        <el-col :span="3">
                            <el-input v-model="currentRowData.edatequantum" readonly></el-input>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="3"><span>申请时间：</span></el-col>
                        <el-col :span="9">
                            <el-input v-model="currentRowData.time" readonly></el-input>
                        </el-col>
                        <el-col :span="3"><span>申请人：</span></el-col>
                        <el-col :span="9">
                            <el-input v-model="currentRowData.applicant" readonly></el-input>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="3"><span>申请人单位（部门）：</span></el-col>
                        <el-col :span="9">
                            <el-input v-model="currentRowData.groupname" readonly></el-input>
                        </el-col>
                        <el-col :span="3"><span>联系电话：</span></el-col>
                        <el-col :span="9">
                            <el-input v-model="currentRowData.phone" readonly></el-input>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="3"><span>申请单位（部门）负责人：</span></el-col>
                        <el-col :span="5">
                            <el-input v-model="currentRowData.sqfzrTrue" readonly></el-input>
                        </el-col>
                        <el-col :span="3"><span>申请单位（部门）负责人意见：</span></el-col>
                        <el-col :span="5">
                            <el-input v-model="currentRowData.fzryjTrue" readonly></el-input>
                        </el-col>
                        <el-col :span="3"><span>申请单位（部门）负责人意见确认时间：</span></el-col>
                        <el-col :span="5">
                            <el-input v-model="currentRowData.fzrqrsjTrue" readonly></el-input>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20" v-show="currentRowData.yysend === '永耀集团'">
                        <el-col :span="3">
                            <span>综合管理部负责人：</span>
                        </el-col>
                        <el-col :span="5">
                            <el-input v-model="currentRowData.zhglbfzrTrue" readonly></el-input>
                        </el-col>
                        <el-col :span="3"><span>综合管理部意见：</span></el-col>
                        <el-col :span="5">
                            <el-input v-model="currentRowData.zhglbyjTrue" readonly></el-input>
                        </el-col>
                        <el-col :span="3"><span>综合管理部意见确认时间：</span></el-col>
                        <el-col :span="5">
                            <el-input v-model="currentRowData.zhglbqrsjTrue" readonly></el-input>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20" v-show="currentRowData.yysend !== '本公司' && currentRowData.yysend !== '永耀集团'">
                        <el-col :span="3"><span>总公司负责人：</span></el-col>
                        <el-col :span="5">
                            <el-input v-model="currentRowData.zgsleaderTrue" readonly></el-input>
                        </el-col>
                        <el-col :span="3"><span>总公司负责人意见：</span></el-col>
                        <el-col :span="5">
                            <el-input v-model="currentRowData.zgsleaderyjTrue" readonly></el-input>
                        </el-col>
                        <el-col :span="3"><span>总公司负责人意见确认时间：</span></el-col>
                        <el-col :span="5">
                            <el-input v-model="currentRowData.zgsleadersjTrue" readonly></el-input>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="3"><span>申请理由：</span></el-col>
                        <el-col :span="21">
                            <el-input type="textarea" v-model="currentRowData.yysy" readonly></el-input>
                        </el-col>
                    </el-row>
                </el-tab-pane>
                <el-tab-pane label="流程信息">
                    <Table
                        :tableData="processTableData"
                        :tableOptions="processTableOptions"
                        :loading="processLoading">
                        <template slot="transdate" slot-scope="scope">
                            {{formatDate(scope.row.transdate)}}
                        </template>
                    </Table>
                </el-tab-pane>
                <el-tab-pane label="附件信息">
                    <Table
                        :tableData="appendixTableData"
                        :tableOptions="appendixTableOptions"
                        :loading="appendixLoading">
                        <template slot="uploaddate" slot-scope="scope">
                            {{formatDate(scope.row.uploaddate)}}
                        </template>
                        <template slot="operate" slot-scope="scope">
                            <el-button type="text" @click="downloadFile(scope.row)">下载</el-button>
                        </template>
                    </Table>
                </el-tab-pane>
            </el-tabs>
            <div class="btn-box">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </div>
        </el-dialog>

        <el-dialog title="电子表格" :visible.sync="previewDialogVisible">
            <iframe :src="pdfSrc" frameborder="0" style="width: 100%; height: 65vh"></iframe>
        </el-dialog>
    </div>
</template>
<script>
import {
    getyyglQueryList,
    getYyglFile,
    getWorkFlowList,
    downloadYyglFile,
    exportWord,
    exportPdf
} from '@/api/yygl'
import Table from '../components/MainTable'
import Pagination from '../components/Pagination'
import Dropdown from '../components/ColumnDropdown'
import dayjs from 'dayjs'
import fileDownload from 'js-file-download'
export default {
    name:'index',
    components:{Table, Pagination, Dropdown},
    data(){
        return {
            searchInput:'',
            tableData:[],
            tableOptions:[
                {label: '项目编号', prop:'bh'},
                {label: '流程阶段', prop:'lcJdmc'},
                {label: '需求描述', prop:'type'},
                {label: '申请理由', prop:'yysy'},
                {label: '用印呈送', prop:'yysend'},
                {label: '印章类型', prop:'yzfl'},
                {label: '用印份数', prop:'yycount', width: 100, slot: true},
                {label: '外借期限', prop:'wjDate', width: 160},
            ],
            realTableOptions:[],
            loading:false,
            queryParam: {
                current: 1,
                size: 10,
            },
            total:0,
            currentSelectId:'',
            currentRowData:{},
            dialogVisible:false,
            processTableData:[],
            processTableOptions:[
                {label: '部门', prop: 'groupname', sortable:false},
                {label: '流程名称', prop: 'lcJdmc', sortable:false},
                {label: '处理人账号', prop: 'personzgh', sortable:false},
                {label: '处理人姓名', prop: 'personname', sortable:false},
                {label: '处理日期', prop: 'transdate', sortable:false, slot: true},
                {label: '意见', prop: 'feed', sortable:false},
            ],
            processLoading:false,
            appendixTableData:[],
            appendixTableOptions:[
                {label: '文件名', prop:'filename'},
                {label: '上传时间', prop:'uploaddate', slot: true},
                {label: '操作', prop:'operate', sortable:false, slot: true},
            ],
            appendixLoading:false,
            previewDialogVisible:false,
            pdfSrc:''
        }
    },
    mounted(){
        this.getTableList()
    },
    methods:{
        formatDate(date){
            return date ? dayjs(date).format('YYYY-MM-DD HH:mm:ss') : date
        },
        //获取新表格列表
        getNewArr(newTableOptions){
            this.realTableOptions = [...newTableOptions]
        },
        async getTableList(){
            this.loading = true
            const params = {...this.queryParam, CID: this.searchInput}
            await getyyglQueryList(params).then(res=>{
                this.tableData = res.records || []
                this.total = res.total || 0
                this.$refs.yyglSearchTable.setCurrent(this.tableData[0])
                this.loading = false
            }).catch(err=>{
                this.loading = false
            })
        },
        //模糊查询和刷新
        handleRefresh(queryParam){
            this.queryParam = {...queryParam}
            this.getTableList()
        },
        //获取当前点击的数据id
        getCurrentData(val){
            //初始化
            this.currentRowData = {}
            this.currentSelectId = val.id
            const {time, jystartdate,jyedndate} = val
            this.currentRowData = val
            this.currentRowData.time = this.formatDate(time)
            this.currentRowData.jystartdate = this.formatDate(jystartdate)
            this.currentRowData.jyedndate = this.formatDate(jyedndate)
        },
        //查看打开窗口
        handleOpenDialog(){
            if(!this.currentSelectId){
                this.$message({
                    type: 'warning',
                    message: '请先选择一条数据！'
                })
                return 
            }
            this.dialogVisible = true
            this.handleGetWorkFlowData()
            this.handleGetAppendixData()
        },
        //获取流程信息
        async handleGetWorkFlowData(){
            this.processLoading = true
            await getWorkFlowList(this.currentSelectId).then(res=>{
                this.processTableData = res || []
                this.processTableData.map(process=>{
                    const {personname,feed,transdate} = process
                    //不是退回的
                    if(process.isback !== 1){
                        //基本信息：申请单位负责人910202 综合管理部负责人910203 总公司负责人910206
                        if(process.lcJdid == 910202){
                            this.currentRowData.sqfzrTrue = personname
                            this.currentRowData.fzryjTrue = feed
                            this.currentRowData.fzrqrsjTrue = this.formatDate(transdate)
                        }else if(process.lcJdid == 910203){
                            this.currentRowData.zhglbfzrTrue = personname
                            this.currentRowData.zhglbyjTrue = feed
                            this.currentRowData.zhglbqrsjTrue = this.formatDate(transdate)
                        }else if(process.lcJdid == 910206){
                            this.currentRowData.zgsleaderTrue = personname
                            this.currentRowData.zgsleaderyjTrue = feed
                            this.currentRowData.zgsleadersjTrue = this.formatDate(transdate)
                        }
                    }
                })
                this.processLoading = false
            }).catch(err=>{
                this.processLoading = false
            })
        },
        //获取附件数据列表
        async handleGetAppendixData(){
            this.appendixLoading = true
            await getYyglFile(this.currentSelectId).then(res=>{
                this.appendixTableData = res || []
                this.appendixLoading = false
            }).catch(err=>{
                this.appendixLoading = false
            })
            
        },
        //下载附件
        downloadFile(row){
            downloadYyglFile(row.id).then(res=>{
                fileDownload(res, row.filename)
            })
        },
        //电子表格预览
        handlePreview(){
            if(!this.currentSelectId){
                this.$message({
                    type: 'warning',
                    message: '请先选择一条数据！'
                })
                return 
            }
            if(this.currentRowData.lcJdmc !== '完成'){
                this.$message({
                    type: 'warning',
                    message: '该流程未完成，无法预览电子表格！'
                })
                return 
            }
            this.previewDialogVisible = true
            exportPdf(this.currentSelectId).then(res=>{
                const blob = new Blob([res], {
                    type: 'application/pdf'
                })
                this.pdfSrc = window.URL.createObjectURL(blob)
            })
        },
        //导出为word、pdf
        handleExport(type){
            if(!this.currentSelectId){
                this.$message({
                    type: 'warning',
                    message: '请先选择一条数据！'
                })
                return 
            }
            if(this.currentRowData.lcJdmc !== '完成'){
                this.$message({
                    type: 'warning',
                    message: '该流程未完成，无法导出！'
                })
                return 
            }
            if(type === "word"){
                exportWord(this.currentSelectId).then(res=>{
                    fileDownload(res,`${this.currentRowData.bh}.doc`)
                })
            }else if(type === 'pdf'){
                exportPdf(this.currentSelectId).then(res=>{
                    fileDownload(res,`${this.currentRowData.bh}.pdf`)
                })
            }
            
        }
    }
}
</script>
<style lang="scss" scoped>
.search-container{
    width: 100%;
    height: 100%;

    .operate-pannel{
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;

        .search-box{
            display: flex;
            .el-input{
                width: 220px;
                margin-right: 5px;
            }
        }
    }
    .table-box{
        height: calc(100% - 80px);

        ::v-deep .el-pagination{
            margin-top: 10px;
            text-align:center;
        }
    }
}

::v-deep .el-dialog__body{
    height: calc(100% - 54px);

    .el-tabs{
        height: calc(100% - 30px);
        // overflow: auto;

        .el-tabs__content{
            //39px el-tabs header高度
            height: calc(100% - 39px);
            overflow: auto;

            .el-tab-pane{
                height: 100%;
            }
        }
    }

    .btn-box{
        margin-top: 10px;
        text-align: center;
    }
}

.el-row{
    margin-bottom: 20px;
}
</style>