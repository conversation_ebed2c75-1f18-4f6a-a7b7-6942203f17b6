<template>
<div>
    <el-form :model="submitForm" :rules="rules" ref="submitForm" label-width="100px" v-watermark="{label: watermark}">
        <el-form-item label="备注" prop="feeds">
            <el-input type="textarea" v-model="submitForm.feeds" placeholder="请输入备注"/>
        </el-form-item>
        <el-form-item label="发送人" prop="sendPerson">
            <el-input 
                v-model="submitForm.sendPerson" 
                @click.native="handleClickInput" 
                placeholder="请选择发送人"
                readonly></el-input>
        </el-form-item>
        <el-form-item>
            <div style="text-align: right">
                <el-button type="primary" @click="handleSubmit('submitForm')">确定</el-button>
                <el-button type="primary" v-if="yyType === 1" @click="handleBack">退回</el-button>
                <el-button @click="$emit('handleClose')">关闭</el-button>
            </div>
        </el-form-item>
    </el-form>
    <el-dialog title="人员选择" width="50%" :visible.sync="personDialogVisible" append-to-body>
        <MainTable
            :tableData="tableData"
            :tableOptions="tableOptions"
            :loading="loading"
            @getCurrentData="getCurrentData"
            style="height: 300px"/>
        <div style="text-align: right; margin-top:10px">
            <el-button type="primary" @click="showAll">显示全部</el-button>
            <el-button type="primary" @click="showComAll">显示本公司全部</el-button>
            <el-button type="primary" @click="handleChoose">选择</el-button>
            <el-button @click="personDialogVisible = false">关闭</el-button>
        </div>
    </el-dialog>
</div>
</template>

<script>
import MainTable from '../MainTable'
import {
    getFlowUsers
} from '@/api/yygl'
export default {
    name: 'index',
    components:{MainTable},
    props:{
        //0：添加  1：处理 
        yyType:{
            type: Number,
            default: 0
        },
        rowData:{
            type: Object,
            default: ()=>{}
        }
    },
    data(){
        return {
            submitForm:{
                feeds:'同意',
                sendPerson:''
            },
            rules:{
                sendPerson:[{required: true, message: '请选择发送人', trigger:'change'}]
            },
            personDialogVisible: false,
            tableData: [],
            tableOptions:[
                {label: '阶段名称', prop: 'jdMc'},
                {label: '工号', prop: 'loginName'},
                {label: '姓名', prop: 'realName', width: 80},
                {label: '部门', prop: 'groupName'},
            ],
            loading:false,
            params:{
                permission: '',
                byAll: 1,//是否全部人员
                byCom: 1//是否本公司全部人员
            },
            currentRowData:{}
        }
    },
    computed:{
        watermark(){
            return this.$store.state.user.watermark
        }
    },
    mounted(){
        
    },
    methods:{
        //首次点击提交按钮获取发送人人员数据 
        //判断是否完成或唯一人选（不允许点击显示选人表格）
        getFlowData(){
            const {id,lcJdid} = this.rowData
            this.params.permission= `${lcJdid ? lcJdid : '910201'},${id}` //流程阶段ID
            getFlowUsers(this.params).then(res=>{
                if(res.success){
                    if(res.result === '完成'){ //没有发送人 完成阶段
                        this.submitForm.sendPerson = '完成'
                        return
                    }else if(res.result.length === 1){ //只有一个发送人 默认选择该发送人
                        this.currentRowData = {...res.result[0]} //发送人信息赋值
                        this.submitForm.sendPerson = this.currentRowData.realName
                        return
                    }
                }
            })
        },
        //获取发送人人员表格数据
        getFlowTableData(){
            this.loading = true
            getFlowUsers(this.params).then(res=>{
                this.tableData = res.result || []
                this.loading = false
            }).catch(err=>{
                this.loading = false
            })
        },
        //选择发送人弹窗
        handleClickInput(){
            //没有发送人 打开选择发送人表格
            if(!this.submitForm.sendPerson){
                this.personDialogVisible = true
                this.params.byAll = 0
                this.params.byCom = 0
                this.getFlowTableData()
            }
            
        },
        //点击的当前行人员信息
        getCurrentData(val){
            this.currentRowData = val
        },
        //确认人员选择
        handleChoose(){
            if(!this.currentRowData.id){
                this.$message({
                    type:'warning',
                    message:'请选择发送人！'
                })
                return
            }
            this.personDialogVisible = false
            const {realName} = this.currentRowData
            this.submitForm.sendPerson = realName
        },
        //确认提交
        handleSubmit(formName){
            this.$refs[formName].validate((valid)=>{
                if(valid){
                    const {loginName, groupId, jdId} = this.currentRowData
                    const info = {
                        sendPersonZgh: loginName,
                        sendPersonGroup: groupId,
                        sendPersonJdid: jdId
                    }
                    this.$emit('handleSubmit', {...this.submitForm, ...info})
                }else{
                    this.$message({
                        type:'warning',
                        message:'请确认表单是否填写完整！'
                    })
                    return false
                }
            })
        },
        //流程退回
        handleBack(){
            this.$confirm('确认退回？').then(_ =>{
                const info = {
                    feeds: this.submitForm.feeds === '同意' ? '退回' : this.submitForm.feeds
                }
                this.$emit('handleBack', info)
            }).catch(_ =>{
                this.$message({
                    type: 'info',
                    message: '已取消退回！'
                })
            })
        },
        //显示全部
        showAll(){
            this.params.byAll = 1; 
            this.params.byCom = 1; 
            this.getFlowTableData()
        },
        //显示本公司全部
        showComAll(){
            this.params.byAll = 0; 
            this.params.byCom = 1; 
            this.getFlowTableData()
        }
        
    }
}
</script>

<style lang="scss" scoped>

</style>