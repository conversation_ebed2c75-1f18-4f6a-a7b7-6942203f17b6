<template>
  <el-dropdown :hide-on-click="false" trigger="click">
    <el-button type="text" class="el-icon-s-operation" style="margin-left: 10px">列表</el-button>
    <el-dropdown-menu slot="dropdown">
        <el-checkbox-group v-model="checkedOptions">
            <el-dropdown-item @click.native="handleReset" class="reset">重置</el-dropdown-item>
            <el-dropdown-item 
              v-for="(item,index) in colOptions" 
              :key="index" 
              :divided="index === 0 ? true: false">
                <el-checkbox :label="item" :key="item"></el-checkbox>
            </el-dropdown-item>
        </el-checkbox-group>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
export default {
  name: 'index',
  props:{
    columnArr:{
      type: Array,
      default: ()=>[]
    }
  },
  data(){
    return {
      colOptions:[],//原数组
      checkedOptions:[],//已选选项数组
    }
  },
  watch:{
    //监听选项
    checkedOptions(valArr){
      const newArr = this.columnArr.filter(item => valArr.indexOf(item.label) !== -1 )
      //已选选项新列表
      this.$emit('getNewArr',newArr)
    }
  },
  mounted(){
    //对象数组计算成字符串数组
    this.colOptions = this.columnArr.map(item=>{
        return item.label
    })
    this.handleReset()
  },
  methods:{
    //初始化全选
    handleReset(){
      this.checkedOptions = [...this.colOptions]
    }
  }
}
</script>

<style lang="scss" scoped>
  .reset{
    text-align: right;
  }
</style>