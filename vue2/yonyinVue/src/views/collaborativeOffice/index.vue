<template>
    <!-- <el-header :style="{background: theme}">Header</el-header> -->
    <el-container>
        <div style="width: 200px">
            <Logo :collapse="false"/>
            <el-scrollbar wrap-class="scrollbar-wrapper">
                <el-menu
                    :default-active="$route.path"
                    router
                    :unique-opened="true"
                    class="el-menu-vertical-demo"
                    :active-text-color="theme">
                    <component
                        v-for="route in menuList"
                        :key="route.url"
                        :index="route.text"
                        :is="(route.children && route.children.length > 0) ? 'el-submenu' : 'el-menu-item'">
                        <template slot="title">
                            <i class="iconfont font-size14" style="margin-right:10px">&#xe790;</i>
                            <span slot="title" class="font-size14">{{route.text}}</span>
                        </template>
                        <template v-if="route.children && route.children.length > 0">
                            <el-menu-item
                                v-for="routeChild in route.children"
                                :key="routeChild.url"
                                :index="routeChild.url"
                                :route="{path: routeChild.url}">
                                <span slot="title">{{routeChild.text}}</span>
                            </el-menu-item>
                        </template>
                    </component>
                    <!-- <el-menu-item
                        v-for="(item,index) in menuList"
                        :key="index"
                        :index="item.url"
                        :route="{path: item.url}">
                        <span slot="title">{{item.name}}</span>
                    </el-menu-item> -->
                </el-menu>
            </el-scrollbar>
        </div>
        <div class="yy-main-container">
            <Header/>
            <el-main :style="{background: `linear-gradient(to bottom, ${theme} 40%, #eaeff3 40% 100%)`}">
                <div class="route-container">
                    <router-view/>
                </div>
            </el-main>
        </div>
    </el-container>
</template>
<script>
import Header from '@/components/CommonNavbar'
import Logo from '@/layout/components/Sidebar/Logo'
import {getModule} from '@/api/yygl'
export default {
    name:'index',
    components:{Header,Logo},
    data(){
        return {
            menuList:[
                {
                    text: '用印管理',
                }
            ]
        }
    },
    computed:{
        theme(){
            return this.$store.state.settings.theme
        }
    },
    created(){
        getModule().then(res=>{
            
        })
        const routesJson = '[{"id":"9101","text":"用印管理添加","url":"/yygl/yyglAdd","Parameter":0,"leaf":true,"DisplayName":""},{"id":"9102","text":"用印管理流程","url":"/yygl/yyglProcess","Parameter":0,"leaf":true,"DisplayName":""},{"id":"9103","text":"用印管理查询","url":"/yygl/yyglSearch","Parameter":0,"leaf":true,"DisplayName":""},{"id":"9104","text":"用印统计查询","url":"/yygl/yytjSearch","Parameter":0,"leaf":true,"DisplayName":""}]'
        const menuList = JSON.parse(routesJson)
        //TODO 默认跳转到第一个
        this.menuList[0].children = menuList
        this.$router.push(menuList[0].url)
    }
}
</script>
<style lang="scss" scoped>
.el-container{
    height: 100%;
    .el-header{
        border-bottom: #ffffff55 1px solid;
    }
    .el-aside{
        height: 100%;
        background: #fff;
    }
    .yy-main-container{
        width: calc(100% - 200px);
        height: 100%;
        position: relative;
        display: flex;
        flex-direction: column;
    }
    .el-main{
        .route-container{
            background: #fff;
            border-radius: 10px;
            height: 100%;
            width: 100%;
            padding: 20px;
            
            .yygl-container{
                height: 100%;
                width: 100%;
                display: flex;
                justify-content: space-between;
            }
        }
    }
}

</style>