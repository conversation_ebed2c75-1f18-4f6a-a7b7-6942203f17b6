<template>
  <div class="login">
    <div class="login-form">
      <div class="trans" @click="changeLoginMode">
        <div class="imgApp"></div>
      </div>
      <transition appear name="fade" @after-leave="handleImgLeave">
        <div class="code" v-show="loginMode == 2">
          <!-- <img alt="scan me!" src="@/assets/images/qrcode.jpg" width="250" height="250" /> -->
          <h1 align="center">扫码登陆</h1>
          <img :src="imgUrl" width="250px" height="250px" />
        </div>
      </transition>
      <transition appear name="fade" @after-leave="handleFormLeave">
        <el-form ref="loginForm" :model="loginForm" :rules="loginRules" v-show="loginMode == 1" class="form">
          <h1 class="title">欢迎登录</h1>
          <h4 class="subtitle">WELCOME TO THE SYSTEM</h4>
          <el-form-item prop="username">
            <el-input v-model="loginForm.username" autocomplete="off" placeholder="请输入工号/手机号/身份证">
              <!-- <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" /> -->
              <i slot="prefix" class="iconfont loginIcon">&#xe788;</i>
            </el-input>
          </el-form-item>
          <!-- <el-form-item prop="password">
          <el-input v-model="loginForm.password" type="password" auto-complete="off" placeholder="密码"
            @keyup.enter.native="handleLogin">
            <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
          </el-input>
        </el-form-item> -->
          <!-- <el-form-item prop="code" v-if="captchaEnabled">
          <el-input v-model="loginForm.code" auto-complete="off" placeholder="验证码" style="width: 63%"
            @keyup.enter.native="handleLogin">
            <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
          </el-input>
          <div class="login-code">
            <img :src="codeUrl" @click="getCode" class="login-code-img" />
          </div>
        </el-form-item> -->
          <el-form-item prop="code" style="margin-top: 30px; margin-bottom: 40px">
            <el-input v-model="loginForm.code" placeholder="请输入验证码" autocomplete="off" @keyup.enter.native="handleLogin">
              <i slot="prefix" class="iconfont loginIcon">&#xe600;</i>
            </el-input>
            <div class="login-code">
              <el-button ref="smsCode" :disabled="!smsButtonVisible" @click="getSmsCode" type="text">
                <span v-show="smsButtonVisible">获取验证码</span>
                <span v-show="!smsButtonVisible">{{ seconds }}秒后获取</span>
              </el-button>
            </div>
          </el-form-item>
          <!-- <el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 25px 0px;">记住密码</el-checkbox> -->
          <el-form-item style="width: 100%">
            <el-button :loading="loading" size="medium" type="primary" @click.native.prevent="handleLogin"
              class="login-button">
              <span v-if="!loading">登&nbsp;&nbsp;&nbsp;&nbsp;录</span>
              <span v-else>登 录 中...</span>
            </el-button>
            <!-- <div v-if="register" style="text-align: center">
              <router-link class="link-type" :to="'/register'">现在注册</router-link>
            </div> -->
          </el-form-item>
        </el-form>
      </transition>
    </div>
    <!--  底部  -->
    <!-- <div class="el-login-footer">
      <span>Copyright © 2018-2022 ruoyi.vip All Rights Reserved.</span>
    </div> -->
    <div class="download-img">
      <div class="download-item">
        <img alt="scan me!" src="@/assets/images/downloadApp.png" width="200" height="200" />
        <div class="download-text">安卓APP下载二维码</div>
      </div>
      <div class="download-item">
        <img alt="scan me!" src="@/assets/images/iosDownload.jpg" width="200" height="200" />
        <div class="download-text">苹果APP下载二维码</div>
      </div>
    </div>
  </div>
</template>

<script>
import throttle from "lodash/throttle";
import { getCodeImg, initScanImage, checkScanResult, sendCode, getScanImage } from "@/api/login";
import Cookies from "js-cookie";
import { getToken } from '@/utils/auth'
export default {
  name: "Login",
  data() {
    return {
      codeUrl: "",
      seconds: 60,
      smsButtonVisible: true,
      loginForm: {
        username: "",
        // password: "admin123",
        // rememberMe: false,
        code: "",
        // uuid: "",
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: "请输入您的账号" },
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入您的密码" },
        ],
        code: [{ required: true, trigger: "change", message: "请输入验证码" }],
      },
      loading: false,
      // 验证码开关
      captchaEnabled: true,
      // 注册开关
      register: true,
      redirect: undefined,
      loginMode: 1,
      // 二维码key
      key: '',
      imgUrl: '',
      baseURL:process.env.VUE_APP_BASE_API
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true,
    },
  },
  // created() {
  //   // this.getCode();
  //   // this.getCookie();
  //   this.getScanImage();
  // },
  mounted() {
    this.getScanImage();
  },
  methods: {
    getCode() {
      getCodeImg().then((res) => {
        this.captchaEnabled =
          res.captchaEnabled === undefined ? true : res.captchaEnabled;
        if (this.captchaEnabled) {
          this.codeUrl = "data:image/gif;base64," + res.img;
          this.loginForm.uuid = res.uuid;
        }
      });
    },
    getCookie() {
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get("rememberMe");
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
      };
    },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          // if (this.loginForm.rememberMe) {
          //   Cookies.set("username", this.loginForm.username, { expires: 30 });
          //   Cookies.set("rememberMe", this.loginForm.rememberMe, {
          //     expires: 30,
          //   });
          // } else {
          //   Cookies.remove("username");
          //   Cookies.remove("password");
          //   Cookies.remove("rememberMe");
          // }
          this.$store
            .dispatch("Login", { ...this.loginForm, Skey: "" })
            .then(() => {
              this.$router.push({ path: this.redirect || "/" }).catch(() => { });
            })
            .catch(() => {
              this.loading = false;
              // if (this.captchaEnabled) {
              //   this.getCode();
              // }
            });
        }
      });
    },
    // this.$('.code').show();
    // this.$('.imgApp').hide();
    changeLoginMode: throttle(function () {
      this.loginMode = 3;
    }, 1100),
    handleImgLeave() {
      this.loginMode = 1;
    },
    handleFormLeave() {
      this.loginMode = 2;
    },
    getSmsCode() {
      if (this.loginForm.username == '') {
        this.$message({type: 'error', message: '请先输入电话号码'});
        return;
      }
      this.smsButtonVisible = false;
      let data = {
        loginName: this.loginForm.username,
      };
      sendCode(data)
        .then((res) => {
          this.$message({
            type: "success",
            message: "验证码已发送，请注意查收！",
          });
          const interval = setInterval(() => {
            this.seconds--;
            if (this.seconds <= 0) {
              clearInterval(interval);
              this.seconds = 60;
              this.smsButtonVisible = true;
            }
          }, 1000);
        })
        .catch(() => {
          this.smsButtonVisible = true;
        });
    },
    // 获取登录二维码
    getScanImage() {
      getScanImage().then((res) => {
        // console.log(res);
        if(res.success == true) {
          this.key = res.skey;
          // let list = res.image.split('');
          // list = list.map((item) => {
          //   return item.charCodeAt().toString(2);
          // })
          // list = list.join('');
          // console.log(list);
          let base64Str = "data:image/png;base64," + res.image;
          this.imgUrl = base64Str;
          // let blob = new Blob([res.image], {type: 'image/jpeg'});
          // let url = window.URL.createObjectURL(blob);
          // this.imgUrl = url;
          this.getState();
        } else {
          // this.getScanImage();
        }
      })
      // initScanImage().then(async (res) => {
      //   this.key = res.skey;
      //   this.imgUrl = this.baseURL + "/wpframe" + res.surl.slice(1);
      //   // 轮询查询结果
      //   this.getState();
      // }).catch(() => {

      // })
    },
    getState() {
      checkScanResult(this.key).then((res) => {
        //
        if (getToken()) {
        } else if (res.success == true) {
          this.$store
            .dispatch("Login", { username: '', code: '', Skey: this.key })
            .then(() => {
              this.$router.push({ path: this.redirect || "/" }).catch(() => { });
            })
            .catch(() => {
              this.$alert('登录失败，请刷新重试', {
                confirmButtonText: '确定',
                callback: (action) => {
                  location.reload();
                }
              })
            });
        } else {
          this.checkAgain();
        }
      }).catch((err) => {
        // console.log(err);
      });

    },
    checkAgain() {
      setTimeout(() => {
        this.getState();
      }, 2000)
    }
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.loginIcon {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 75%;
  color: #4877fb;
  font-size: 25px;
}

.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url(~@/assets/images/login-background1.jpg);
  background-size: cover;
}

.title {
  margin: 0px auto 10px auto;
  text-align: left;
  font-weight: bold;
  color: #565656;
}

.subtitle {
  margin: 0px auto 30px auto;
  color: #a7a7a7;
  font-weight: 100;
  margin-bottom: 45px;
}

.login-form {
  border-radius: 6px;
  background: #ffffff;
  width: 480px;
  height: 480px;
  padding: 25px 25px 5px 25px;
  position: absolute;
  overflow: hidden;
  left: 10%;

  ::v-deep .el-input {
    height: 68px;

    input {
      height: 68px;
      font-size: 1.4em;
    }
  }

  // .input-icon {
  //   height: 56px;
  //   width: 14px;
  //   margin-left: 2px;
  // }

  ::v-deep .el-input__inner {
    border-radius: 0px;
    border-top-width: 0px;
    border-left-width: 0px;
    border-right-width: 0px;
    border-bottom-width: 1px;
    padding-left: 40px;
    /*outline: medium;*/
  }

  ::v-deep .el-form-item__error {
    font-size: 1.2em;
  }
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.login-code {
  width: 25%;
  height: 68px;
  position: absolute;
  right: 0;
  top: 20px;

  img {
    cursor: pointer;
    vertical-align: middle;
  }

  span {
    font-size: 1.3em;
  }
}

.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}

.login-code-img {
  height: 38px;
}

.trans {
  position: absolute;
  right: 20px;
  top: 20px;
  cursor: pointer;
  width: 80px;
  height: 80px;
  background-image: url(~@/assets/images/qrcode.jpg);
  background-color: #2b96e6; //图标优化 -- 换为白色线条图标 背景层添加背景色
  background-size: 100% 100%;
}

.imgApp {
  width: 80px;
  height: 80px;
  background: linear-gradient(225deg, transparent 50%, #fff 0);
}

.code {
  width: 100%;
  height: 100%; //将登录框挤出显示区域
  text-align: center;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}

.login-button {
  margin-top: 15px;
  height: 55px;
  width: 100%;
  border-radius: 27.5px;

  ::v-deep span {
    font-size: 1.4em;
  }
}

.download-img {
  position: absolute;
  right: 50px;
  bottom: 50px;
  display: flex;
  .download-item{
    margin-right: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .download-text{
      font-size: 14px;
      font-weight: bold;
      color:#333;
    }
  }
}
</style>
