<template>
    <el-select 
        v-model="search" 
        remote
        filterable 
        clearable
        :loading="loading"
        :remote-method="remoteMethod"
        @change="handleChange" 
        placeholder="请输入应用名称"
        value-key="Id">
        <template slot="prefix">
            <i class="el-icon-search"></i>
        </template>
        <el-option 
            v-for="item in options" 
            :key="item.Id"
            :value="item"
            :label="item.module_name + ' / ' + item.ParentName + ' / ' + item.Title"
            @click.native="handleOpenModule">
        </el-option>
    </el-select>
</template>

<script>
import {getFunctionByOption} from '@/api/home'
import {OpenMoudleWindow} from '@/utils/jiaohu'
export default {
    name:'search',
    data(){
        return {
            search:'',
            loading: false,
            options:[],
            storageOptions:[],
            maxStorageLength: 5
        }
    },
    mounted(){
        this.storageOptions = JSON.parse(window.localStorage.getItem('appSearchOption')) || []
        this.options = [...this.storageOptions]
    },
    methods:{
        //输入大于两个字 进行远程搜索
        remoteMethod(val){
            if(val !== '' && val.length >= 2){
                this.loading = true
                getFunctionByOption({searchp: val}).then(res=>{
                    this.options = res.Table || []
                    this.loading = false
                })
            }else{
                this.options = [...this.storageOptions]
            }
        },
        //选择结果改变 打开应用
        handleChange(){
            //存储搜索记录到本地
            const has = this.storageOptions.find(item => item.Id === this.search.Id)
            //存在或者没有搜索内容
            if(has || !this.search){
                this.options = [...this.storageOptions]
                return
            }else{
                //显示最近五条搜索记录
                if(this.storageOptions.length >= this.maxStorageLength){
                    this.storageOptions.splice(this.storageOptions.length - 1, 1)
                }
                this.storageOptions.unshift({...this.search})
                window.localStorage.setItem('appSearchOption',JSON.stringify(this.storageOptions))
            }
        },
        //打开应用模块
        handleOpenModule(){
            const {module_mix, module_url,Id,Url,Title} = this.search
            OpenMoudleWindow(module_mix, module_url,Id,Url,Title)
        }
    }
}
</script>

<style lang="scss" scoped>
::v-deep .el-input{
    height: 35px;
    .el-input__prefix{
        margin-left: 6px;
    }
    .el-input__inner{
        border-radius: 100px;
    }
}

.option-item{
    line-height: 28px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    &:hover{
        cursor: pointer;
        background: #f5f7fa;
    }
}

</style>