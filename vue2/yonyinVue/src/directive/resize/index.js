export default {
  bind(el, binding) { // el为绑定的元素，binding为绑定给指令的对象
    let width = '', height = '';
    let flag = 0;

    function isReize() {
      const style = document.defaultView.getComputedStyle(el);
      if (width !== style.width || height !== style.height) {
        if (flag < 10) {
          binding.value(style.height);  // 关键
          flag++;
        } else {

        }

        // console.log(flag);
        clearInterval(el.__vueSetInterval__);
      }
      width = style.width;
      height = style.height;
    }
    el.__vueSetInterval__ = setInterval(isReize, 300);
  },
  unbind(el) {
    clearInterval(el.__vueSetInterval__);
  }
}