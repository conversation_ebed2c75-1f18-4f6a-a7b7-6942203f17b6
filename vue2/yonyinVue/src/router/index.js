import Vue from 'vue'
import VueRouter from 'vue-router'
import Layout from '@/layout'
import Login from '@/views/login'

Vue.use(VueRouter)

export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect')
      }
    ]
  },
  {
    path: '/login',
    // component: () => import('@/views/login'),
    component: Login,
    hidden: true
  },
  // {
  //   path: '/register',
  //   component: () => import('@/views/register'),
  //   hidden: true
  // },
  {
    path: '/404',
    component: () => import('@/views/error/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true
  },
  {
    path: "*",
    name: "NotFound",
    component: () => import("@/views/error/404"),
  },
  {
    path: '',
    component: Layout,
    redirect: 'index',
    children: [
      {
        path: 'index',
        component: () => import('@/views/index'),
        name: 'Index',
        meta: { title: '首页', icon: 'dashboard', affix: true }
      }
    ]
  },
  //协同办公路由
  {
    path: '/yygl',
    component: () => import('@/views/collaborativeOffice/index'),
    // redirect: 'index',
    children:[
      {
        path: 'yygladd',
        name: 'yygladd',
        component: () => import('@/views/collaborativeOffice/yyglAdd/index'),
      },
      {
        path: 'yyglprocess',
        name: 'yyglprocess',
        component: () => import('@/views/collaborativeOffice/yyglProcess/index'),
      },
      {
        path: 'yyglsearch',
        name: 'yyglsearch',
        component: () => import('@/views/collaborativeOffice/yyglSearch/index'),
      },
      {
        path: 'yytjsearch',
        name: 'yytjsearch',
        component: () => import('@/views/collaborativeOffice/yytjSearch/index'),
      },
    ]
  }
  // {
  //   path: '/user',
  //   component: Layout,
  //   hidden: true,
  //   redirect: 'noredirect',
  //   children: [
  //     {
  //       path: 'profile',
  //       component: () => import('@/views/system/user/profile/index'),
  //       name: 'Profile',
  //       meta: { title: '个人中心', icon: 'user' }
  //     }
  //   ]
  // }
]

const router = new VueRouter({
  mode: 'history',
  scrollBehavior: () => ({ y: 0 }),
  base: process.env.NODE_ENV === 'development' ? '/' : process.env.VUE_APP_BASE_API,
  routes:constantRoutes
})

export default router
