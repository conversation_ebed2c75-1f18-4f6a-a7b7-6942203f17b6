import request from '@/utils/request'

/**
 * 模块查找
 * @param {*} params 
 * @returns 
 */
export function getFunctionByOption(params){
    return request({
        url: '/wpframe/Service/MetaMan/GetFunctionByOption',
        method: 'get',
        params
    })
}

/**
 * 获取用户切换列表
 * @returns 
 */
export function getCurrentUccOptionList(){
    return request({
        url: '/wpframe/Service/Frame/GetCurrentUccOptionList',
        method: 'get'
    })
}

/**
 * 获取用户活跃度
 * @returns 
 */
export function getURDList(){
    return request({
        url:'/wpframe/Service/URDMan/GetURDList',
        method: 'get'
    })
}

/**
 * 获取邮件
 * @returns 
 */
 export function getUserEmailList(){
    return request({
        url:'/wpframe/Service/MetaMan/GetUserEmailList',
        method: 'get'
    })
}

/**
 * 获取邮件详情
 * @param {*} EmailId 
 * @returns 
 */
export function getEmailDetailList(EmailId){
    const params = {
        EmailId
    }
    return request({
        url: '/wpframe/Service/MetaMan/GetEmailDetailList',
        method: 'get',
        params
    })
}

/**
 * 获取分包看报数据
 * @returns 
 */
export function getFBKBData(){
    return request({
        url:'/wpframe/Service/MetaMan/GetFBKBData',
        method: 'get'
    })
}

// 获取待办事项*
export function getScheduleList(params) {
    return request({
      url: '/wpframe/Service/DBLMan/GetDBData',
      method: 'get',
      params
    })
}

/**
 * 获取待办详情
 * @param {*} lcid 
 * @returns 
 */
export function getDBDetailData(lcid){
    const params = {
        lcid
    }
    return request({
        url: '/wpframe/Service/DBCMan/GetDBDetailData',
        method: 'get',
        params
    })
}