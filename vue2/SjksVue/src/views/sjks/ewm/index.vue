<template>
  <div class="main" ref="main">
    <div class="process-info" style="width: 100%;height: 100%">
      <div class="search-container">
        <div class="operate-pannel">
          <div class="search-box">
            <el-button type="text" size="mini" icon="el-icon-refresh"
                       @click="handleRefresh">
              刷新
            </el-button>
            <el-button type="text" size="mini" icon="el-icon-download"
                       v-if="this.$store.getters.permissions.indexOf('JDWSJ02EWM01QX02') > -1"
                       @click="openGenerateDialog">
              生成
            </el-button>
            <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr" />
          </div>
        </div>
        <div class="table-box">
          <Table :tableData="list" :tableOptions="realTableOptions" :loading="loading">
            <template slot-scope="scope">
              {{
                (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
              }}
            </template>
            <template slot="qrCode" slot-scope="scope">
              <el-popover
                placement="right"
                width="200"
                trigger="hover"
                :open-delay="300"
                @after-enter="generateQrCode(scope.row.qrCode, scope.row.qrCode)"
                @after-leave="clearQrCode(scope.row.qrCode)">
                <div class="qr-code-container">
                  <div :id="`qrcode-${scope.row.qrCode}`" class="qr-code"></div>
                  <p class="qr-code-text">{{ scope.row.qrCode }}</p>
                </div>
                <span slot="reference" class="qr-code-number">{{ scope.row.qrCode }}</span>
              </el-popover>
            </template>
            <template slot="userId" slot-scope="scope">
              <span v-if="scope.row.userId">是</span>
              <span v-else>否</span>
            </template>
          </Table>
          <Pagination @handleRefresh="handleCurrentChange" :queryParam="queryParams"
                      layout="total, sizes, prev, pager, next, jumper" :total="queryParams.total" />
        </div>
      </div>
    </div>

    <!-- 生成二维码弹窗 -->
    <el-dialog title="生成二维码" :visible.sync="generateDialogVisible" width="400px" :close-on-click-modal="false">
      <el-form ref="generateForm" :model="generateForm" :rules="generateRules" label-width="100px">
        <el-form-item label="生成数量" prop="quantity">
          <el-input-number
            v-model="generateForm.quantity"
            :min="1"
            :max="1000"
            placeholder="请输入生成数量"
            style="width: 100%">
          </el-input-number>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="generateDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleGenerate" :loading="generateLoading">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Table from 'components/MainTable/index.vue'
import Pagination from 'components/Pagination/index.vue'
import Dropdown from 'components/ColumnDropdown/index.vue'
import {getList, generateQrcode} from "api/sjks/qrcode";
import {downLoad} from "@/utils/tool";
import QRCode from 'qrcodejs2';

export default {
  name: 'index',
  components: { Table, Pagination, Dropdown },
  data() {
    return {
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        total: 0,
      },
      realTableOptions: [],
      tableOptions: [
        { label: '二维码编号', prop: 'qrCode', slot: true },
        { label: '是否绑定', prop: 'userId', slot: true },
        { label: '绑定时间', prop: 'bindingTime' },
      ],
      loading: false,
      list: [],
      // 生成弹窗相关
      generateDialogVisible: false,
      generateLoading: false,
      generateForm: {
        quantity: 1
      },
      generateRules: {
        quantity: [
          { required: true, message: '请输入生成数量', trigger: 'blur' },
          { type: 'number', min: 1, max: 1000, message: '数量必须在1-1000之间', trigger: 'blur' }
        ]
      },
      // 二维码相关
      qrCodeInstance: null
    };
  },
  methods: {
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },

    /** 分页查询 */
    handleCurrentChange(val) {
      this.queryParams = val
      this.query()
    },


    // 查询方法
    query() {
      this.loading = true
      getList(this.queryParams).then((res) => {
        this.list = res.result.records
        this.queryParams.total = res.result.total
      }).finally(() => {
        this.loading = false
      })
    },

    // 打开生成弹窗
    openGenerateDialog() {
      this.generateDialogVisible = true
      this.generateForm.quantity = 1
      this.$nextTick(() => {
        this.$refs.generateForm.clearValidate()
      })
    },

    // 处理生成
    handleGenerate() {
      this.$refs.generateForm.validate((valid) => {
        if (valid) {
          this.generateLoading = true
          generateQrcode({ quantity: this.generateForm.quantity }).then((res) => {
            // 下载PDF文件
            downLoad(res, `二维码_${new Date().getTime()}.pdf`)
            this.$message.success('生成成功！PDF已开始下载')
            this.generateDialogVisible = false
            this.query() // 刷新列表
          }).catch((error) => {
            this.$message.error(error.message || '生成失败')
          }).finally(() => {
            this.generateLoading = false
          })
        }
      })
    },

    // 刷新按钮点击事件
    handleRefresh() {
      this.query()
      this.$message.success('刷新成功')
    },

    // 生成二维码
    generateQrCode(qrCodeText, elementId) {
      console.log('开始生成二维码:', qrCodeText, elementId)

      // 使用setTimeout确保DOM完全渲染
      setTimeout(() => {
        // 清除之前的二维码
        this.clearQrCode(elementId)

        // 生成新的二维码
        const targetElement = document.getElementById(`qrcode-${elementId}`)
        console.log('目标元素:', targetElement)
        console.log('元素innerHTML:', targetElement ? targetElement.innerHTML : 'null')

        if (targetElement && qrCodeText) {
          try {
            console.log('正在生成二维码...')
            // 确保元素为空
            targetElement.innerHTML = ''

            this.qrCodeInstance = new QRCode(targetElement, {
              text: qrCodeText,
              width: 150,
              height: 150,
              colorDark: '#000000',
              colorLight: '#ffffff',
              correctLevel: QRCode.CorrectLevel.H
            })
            console.log('二维码生成成功')
            console.log('生成后元素内容:', targetElement.innerHTML)
          } catch (error) {
            console.error('生成二维码失败:', error)
          }
        } else {
          console.error('目标元素或二维码文本为空:', targetElement, qrCodeText)
        }
      }, 100) // 100ms延迟确保DOM渲染完成
    },

    // 清除二维码
    clearQrCode(elementId) {
      if (elementId) {
        const targetElement = document.getElementById(`qrcode-${elementId}`)
        if (targetElement) {
          targetElement.innerHTML = ''
        }
      }
      this.qrCodeInstance = null
    }
  },

  created() {
    // 初始化表格列配置
    this.realTableOptions = [...this.tableOptions]
    this.query()
  }
};
</script>
<style lang="scss" scoped>
.main {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: space-between;
}


.process-info {
  height: 100%;
  box-shadow: 0 0 6px #d9e2f1aa;
  border-radius: 10px;
  padding: 10px;
  transition: all .5s;
}

.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 100px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

// 二维码相关样式
.qr-code-container {
  text-align: center;

  .qr-code {
    margin-bottom: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 150px;

    canvas {
      display: block !important;
    }

    img {
      display: block !important;
    }
  }

  .qr-code-text {
    margin: 0;
    font-size: 12px;
    color: #666;
    word-break: break-all;
  }
}

.qr-code-number {
  color: #409EFF;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}

</style>
