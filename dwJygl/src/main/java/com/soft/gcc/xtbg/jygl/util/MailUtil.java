package com.soft.gcc.xtbg.jygl.util;

//import com.soft.framework.config.MailConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;
import javax.mail.MessagingException;
import javax.mail.internet.AddressException;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.io.UnsupportedEncodingException;

@Component
public class MailUtil {

//    @Autowired
//    private JavaMailSender mailSender;

//    @Autowired
//    private MailConfig mailConfig;

//    public void sendSimpleMail(String to, String subject, String text) throws MessagingException, UnsupportedEncodingException {
//        MimeMessage message = mailSender.createMimeMessage();
//        MimeMessageHelper helper = new MimeMessageHelper(message);
//        helper.setFrom(new InternetAddress(mailConfig.getUsername(), "新世洋仓储WMS平台")); // 设置发件人地址和显示名称
//        helper.setTo(to);
//        // 主题
//        helper.setSubject(subject);
//        // 内容
//        helper.setText(text);
//        mailSender.send(message);
//    }
//
//    public void sendHtmlMail(String to, String subject, String html) throws MessagingException, UnsupportedEncodingException {
//        MimeMessage message = mailSender.createMimeMessage();
//        MimeMessageHelper helper = new MimeMessageHelper(message, true);
//        helper.setFrom(new InternetAddress(mailConfig.getUsername(), "新世洋仓储WMS平台"));
//        helper.setTo(to);
//        helper.setSubject(subject);
//        helper.setText(html, true);
//        mailSender.send(message);
//    }

    /**
     * 校验邮箱格式
     * @param email
     * @return
     */
    public boolean isValidEmail(String email) {
        try {
            InternetAddress emailAddr = new InternetAddress(email);
            emailAddr.validate();
            return true;
        } catch (AddressException e) {
            return false;
        }
    }
}
