package com.soft.gcc.xtbg.jygl.controller;

import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.jygl.entity.DfdwJysjDebtmanagement;
import com.soft.gcc.xtbg.jygl.params.DebtmanagementParams;
import com.soft.gcc.xtbg.jygl.service.DfdwJysjDebtmanagementService;
import com.soft.gcc.xtbg.jygl.util.ExcelUtil;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

/**
 * 隐性债券数据
 */
@RestController
@RequestMapping("/debtManagement")
public class DebtManagementController extends BaseController {
    @Resource
    private DfdwJysjDebtmanagementService debtmanagementService;

    @PostMapping("/getList")
    public Result<?> getList(@RequestBody DebtmanagementParams debtmanagementParams){
        PersonEntity user = user();
        if (user == null){
            return Result.error("用户未登录");
        }
        return Result.ok(debtmanagementService.getList(debtmanagementParams,user));
    }

    /**
     * 上传
     */
    @PostMapping("/importExcel")
    public Result<?> importExcel(MultipartFile file){
        return debtmanagementService.importExcel(file);
    }

    /**
     * 获取汇总列表
     */
    @GetMapping("/getSummary")
    public Result<?> getSummary(){
        PersonEntity user = user();
        if (user == null){
            return Result.error("用户未登录");
        }
        return Result.ok(debtmanagementService.getSummary(user));
    }

    /**
     * 导出excel
     */
    @PostMapping("exportExcel")
    public void exportExcel(@RequestBody DebtmanagementParams param, HttpServletResponse response) throws IOException {
        PersonEntity user = user();
        if (user == null){
            throw new RuntimeException("用户未登录");
        }
        //poi 导出excel
        List<DfdwJysjDebtmanagement> excelList = debtmanagementService.getExcelList(param,user);
        String fileName = "隐性债券数据" + System.currentTimeMillis() + ".xls";
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
        ExcelUtil.exportExcelX(excelList, null, "隐性债券数据", DfdwJysjDebtmanagement.class, fileName, response);
//        ExcelUtil.defaultExport(excelList, DfdwJysjBusinessData.class, fileName, response);

    }

    /**
     * 获取单位选项
     */
    @GetMapping("/getCompanyOptions")
    public Result<?> getCompanyOptions(){
        PersonEntity user = user();
        if (user == null){
            return Result.error("用户未登录");
        }
        return Result.ok(debtmanagementService.getCompanyOptions(user));
    }
}
