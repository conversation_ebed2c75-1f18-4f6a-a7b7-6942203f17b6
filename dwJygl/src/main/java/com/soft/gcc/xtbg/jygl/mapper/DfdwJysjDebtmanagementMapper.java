package com.soft.gcc.xtbg.jygl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.gcc.xtbg.jygl.entity.DfdwJysjDebtmanagement;
import io.swagger.models.auth.In;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_JYSJ_DebtManagement(隐性债券数据)】的数据库操作Mapper
* @createDate 2025-07-15 16:15:14
* @Entity generator.domain.DfdwJysjDebtmanagement
*/
public interface DfdwJysjDebtmanagementMapper extends BaseMapper<DfdwJysjDebtmanagement> {

    /**
     * 批量插入数据
     * @param list 要插入的数据列表
     */
    void batchInsert(@Param("list") List<DfdwJysjDebtmanagement> list);

    List<String> getCompanyOptions(@Param("allAdmin") Boolean allAdmin,@Param("topGroupId") Integer topGroupId);
}




