package com.soft.gcc.xtbg.jygl.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.jygl.entity.DfdwJysjCollection;
import com.soft.gcc.xtbg.jygl.params.DfdwJysjCollectionParams;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
* <AUTHOR>
* @description 针对表【DFDW_JYSJ_Collection(经营数据-催收管理)】的数据库操作Service
* @createDate 2025-07-15 14:13:11
*/
public interface DfdwJysjCollectionService extends IService<DfdwJysjCollection> {


    IPage<DfdwJysjCollection> pageList(DfdwJysjCollectionParams param);

    DfdwJysjCollection addOrUpdate(DfdwJysjCollection data) throws IOException;

    void sendEmail(DfdwJysjCollectionParams param);



    /**
     * 生成催办函
     */
    MultipartFile reminderLetter(DfdwJysjCollection collection) throws IOException ;

    /**
     * 生成 工程审计请办函
     */
    MultipartFile auditRequestLetter(DfdwJysjCollection collection) throws IOException;

    /**
     * 生成催款函
     */
    MultipartFile paymentDemandLetter(DfdwJysjCollection collection) throws IOException;

    /**
     * 生成往来款项催告函
     */
    MultipartFile dunningLetter(DfdwJysjCollection collection) throws IOException;

}
